# ResuMatch Troubleshooting Guide

This guide helps resolve common issues when running the ResuMatch application.

## 🚨 Critical Issues Fixed

### Issue 1: File Validation Error (RESOLVED)
**Problem**: `ReferenceError: File is not defined` during server-side rendering.

**Solution**: Updated `frontend/src/utils/validation.ts` to use client-side only validation:
- Added environment detection (`typeof window === 'undefined'`)
- Replaced `z.instanceof(File)` with custom validation function
- Implemented manual file validation for better error messages

### Issue 2: API Connection Error (RESOLVED)
**Problem**: `ERR_CONNECTION_REFUSED` when connecting to backend.

**Solution**: 
- Enhanced error handling in `frontend/src/lib/api.ts`
- Created startup script `start-resumatch.sh`
- Improved CORS configuration
- Added connection status checks

## 🔧 Common Issues & Solutions

### Backend Issues

#### 1. Backend Won't Start
**Symptoms**: 
- `python main.py` fails
- Import errors
- Port already in use

**Solutions**:
```bash
# Check Python version (requires 3.8+)
python --version

# Install dependencies
pip install -r requirements.txt

# Check if port 8000 is in use
lsof -i :8000

# Kill process on port 8000 if needed
lsof -ti:8000 | xargs kill -9
```

#### 2. Missing Dependencies
**Symptoms**: Import errors for packages

**Solutions**:
```bash
# Install all requirements
pip install -r requirements.txt

# Install spaCy model
python -m spacy download en_core_web_lg

# For development
pip install uvicorn fastapi python-multipart
```

#### 3. CORS Errors
**Symptoms**: Browser blocks requests from frontend

**Solutions**: Backend already configured with permissive CORS. If issues persist:
```python
# In resumatch/api/router.py - already implemented
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)
```

### Frontend Issues

#### 1. File Validation Error (FIXED)
**Symptoms**: `File is not defined` error

**Status**: ✅ RESOLVED - Updated validation to be client-side only

#### 2. Node.js Version Issues
**Symptoms**: Build fails, dependency errors

**Solutions**:
```bash
# Check Node.js version (requires 18+)
node --version

# Update Node.js if needed
# Visit: https://nodejs.org/

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### 3. Environment Variables
**Symptoms**: API calls fail, wrong URLs

**Solutions**:
```bash
# Check .env.local exists
ls frontend/.env.local

# Create if missing
cp frontend/.env.example frontend/.env.local

# Verify content
cat frontend/.env.local
```

#### 4. Build Errors
**Symptoms**: TypeScript errors, build failures

**Solutions**:
```bash
cd frontend

# Type check
npm run type-check

# Lint check
npm run lint

# Clear Next.js cache
rm -rf .next

# Rebuild
npm run build
```

### Network Issues

#### 1. Connection Refused
**Symptoms**: Cannot connect to localhost:8000

**Solutions**:
```bash
# Check if backend is running
curl http://localhost:8000/

# Check processes on port 8000
lsof -i :8000

# Start backend manually
python main.py --host 0.0.0.0 --port 8000
```

#### 2. Port Conflicts
**Symptoms**: "Port already in use" errors

**Solutions**:
```bash
# Find process using port 8000
lsof -i :8000

# Kill process
kill -9 <PID>

# Or use different port
python main.py --port 8001
```

### File Upload Issues

#### 1. PDF Upload Fails
**Symptoms**: Upload rejected, validation errors

**Solutions**:
- Ensure file is PDF format
- Check file size (max 10MB)
- Verify backend upload endpoint is working:
```bash
curl -X POST http://localhost:8000/api/documents/upload \
  -F "file=@test.pdf" \
  -F "name=test_resume"
```

#### 2. Progress Not Showing
**Symptoms**: Upload works but no progress indicator

**Solutions**: Check browser console for JavaScript errors

### Database/Storage Issues

#### 1. Resume Not Found
**Symptoms**: 404 errors when accessing resumes

**Solutions**: Backend uses in-memory storage - resumes are lost on restart. This is expected for demo.

#### 2. Jobs Not Persisting
**Symptoms**: Created jobs disappear

**Solutions**: Same as above - in-memory storage resets on restart.

## 🚀 Quick Start Commands

### Automated Startup
```bash
# Start both backend and frontend
./start-resumatch.sh start

# Check status
./start-resumatch.sh status

# Stop services
./start-resumatch.sh stop
```

### Manual Startup
```bash
# Terminal 1: Backend
python main.py --host 0.0.0.0 --port 8000

# Terminal 2: Frontend
cd frontend
npm run dev
```

### Testing Connection
```bash
# Test backend
curl http://localhost:8000/

# Test jobs endpoint
curl http://localhost:8000/api/jobs

# Test frontend
curl http://localhost:3000/
```

## 🔍 Debugging Steps

### 1. Check Logs
```bash
# Backend logs (if using start script)
tail -f backend.log

# Frontend logs (if using start script)
tail -f frontend.log

# Manual backend logs
python main.py  # Shows logs in terminal
```

### 2. Browser Developer Tools
- Open browser DevTools (F12)
- Check Console tab for JavaScript errors
- Check Network tab for failed API requests
- Look for CORS errors or 404s

### 3. API Testing
```bash
# Test health endpoint
curl http://localhost:8000/

# Test with verbose output
curl -v http://localhost:8000/api/jobs

# Test upload endpoint
curl -X POST http://localhost:8000/api/documents/upload \
  -F "file=@sample.pdf"
```

## 📞 Getting Help

### Check These First:
1. ✅ Python 3.8+ installed
2. ✅ Node.js 18+ installed  
3. ✅ Backend running on port 8000
4. ✅ Frontend running on port 3000
5. ✅ No firewall blocking localhost
6. ✅ Dependencies installed

### Log Files:
- Backend: `backend.log` (if using start script)
- Frontend: `frontend.log` (if using start script)
- Browser: Developer Tools Console

### Test URLs:
- Backend API: http://localhost:8000/
- API Docs: http://localhost:8000/docs
- Frontend: http://localhost:3000/

### Common Error Codes:
- **ERR_CONNECTION_REFUSED**: Backend not running
- **404**: Wrong endpoint or backend not configured
- **CORS**: Cross-origin request blocked
- **500**: Backend server error
- **File validation error**: Fixed in latest version

## ✅ Success Indicators

When everything is working correctly:
- ✅ Backend shows "Uvicorn running on http://0.0.0.0:8000"
- ✅ Frontend shows "Ready - started server on 0.0.0.0:3000"
- ✅ `curl http://localhost:8000/` returns JSON
- ✅ Browser loads http://localhost:3000 without errors
- ✅ File upload works without validation errors
- ✅ Job matching returns results
