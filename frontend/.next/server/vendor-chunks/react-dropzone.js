"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dropzone";
exports.ids = ["vendor-chunks/react-dropzone"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.ErrorCode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useDropzone: () => (/* binding */ useDropzone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var file_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/index.js\");\n/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/index.js */ \"(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\");\nvar _excluded = [\n    \"children\"\n], _excluded2 = [\n    \"open\"\n], _excluded3 = [\n    \"refKey\",\n    \"role\",\n    \"onKeyDown\",\n    \"onFocus\",\n    \"onBlur\",\n    \"onClick\",\n    \"onDragEnter\",\n    \"onDragOver\",\n    \"onDragLeave\",\n    \"onDrop\"\n], _excluded4 = [\n    \"refKey\",\n    \"onChange\",\n    \"onClick\"\n];\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n/* eslint prefer-template: 0 */ \n\n\n\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */ var Dropzone = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(_ref, ref) {\n    var children = _ref.children, params = _objectWithoutProperties(_ref, _excluded);\n    var _useDropzone = useDropzone(params), open = _useDropzone.open, props = _objectWithoutProperties(_useDropzone, _excluded2);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function() {\n        return {\n            open: open\n        };\n    }, [\n        open\n    ]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n        open: open\n    })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\nvar defaultProps = {\n    disabled: false,\n    getFilesFromEvent: file_selector__WEBPACK_IMPORTED_MODULE_1__.fromEvent,\n    maxSize: Infinity,\n    minSize: 0,\n    multiple: true,\n    maxFiles: 0,\n    preventDropOnDocument: true,\n    noClick: false,\n    noKeyboard: false,\n    noDrag: false,\n    noDragEventsBubbling: false,\n    validator: null,\n    useFsAccessApi: false,\n    autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n    /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */ children: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */ accept: prop_types__WEBPACK_IMPORTED_MODULE_3__.objectOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.string)),\n    /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */ multiple: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If false, allow dropped items to take over the current browser window\n   */ preventDropOnDocument: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables click to open the native file selection dialog\n   */ noClick: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */ noKeyboard: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables drag 'n' drop\n   */ noDrag: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, stops drag event propagation to parents\n   */ noDragEventsBubbling: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Minimum file size (in bytes)\n   */ minSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Maximum file size (in bytes)\n   */ maxSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */ maxFiles: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Enable/disable the dropzone\n   */ disabled: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */ getFilesFromEvent: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when closing the file dialog with no selection\n   */ onFileDialogCancel: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when opening the file dialog\n   */ onFileDialogOpen: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */ useFsAccessApi: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Set to true to focus the root element on render\n   */ autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */ onDragEnter: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */ onDragLeave: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */ onDragOver: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */ onDrop: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */ onDropAccepted: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */ onDropRejected: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */ onError: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */ validator: prop_types__WEBPACK_IMPORTED_MODULE_3__.func\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropzone);\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */ /**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */ /**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */ /**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */ var initialState = {\n    isFocused: false,\n    isFileDialogActive: false,\n    isDragActive: false,\n    isDragAccept: false,\n    isDragReject: false,\n    acceptedFiles: [],\n    fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */ function useDropzone() {\n    var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props), accept = _defaultProps$props.accept, disabled = _defaultProps$props.disabled, getFilesFromEvent = _defaultProps$props.getFilesFromEvent, maxSize = _defaultProps$props.maxSize, minSize = _defaultProps$props.minSize, multiple = _defaultProps$props.multiple, maxFiles = _defaultProps$props.maxFiles, onDragEnter = _defaultProps$props.onDragEnter, onDragLeave = _defaultProps$props.onDragLeave, onDragOver = _defaultProps$props.onDragOver, onDrop = _defaultProps$props.onDrop, onDropAccepted = _defaultProps$props.onDropAccepted, onDropRejected = _defaultProps$props.onDropRejected, onFileDialogCancel = _defaultProps$props.onFileDialogCancel, onFileDialogOpen = _defaultProps$props.onFileDialogOpen, useFsAccessApi = _defaultProps$props.useFsAccessApi, autoFocus = _defaultProps$props.autoFocus, preventDropOnDocument = _defaultProps$props.preventDropOnDocument, noClick = _defaultProps$props.noClick, noKeyboard = _defaultProps$props.noKeyboard, noDrag = _defaultProps$props.noDrag, noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling, onError = _defaultProps$props.onError, validator = _defaultProps$props.validator;\n    var acceptAttr = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.acceptPropAsAcceptAttr)(accept);\n    }, [\n        accept\n    ]);\n    var pickerTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.pickerOptionsFromAccept)(accept);\n    }, [\n        accept\n    ]);\n    var onFileDialogOpenCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n    }, [\n        onFileDialogOpen\n    ]);\n    var onFileDialogCancelCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n    }, [\n        onFileDialogCancel\n    ]);\n    /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */ var rootRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var _useReducer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, initialState), _useReducer2 = _slicedToArray(_useReducer, 2), state = _useReducer2[0], dispatch = _useReducer2[1];\n    var isFocused = state.isFocused, isFileDialogActive = state.isFileDialogActive;\n    var fsAccessApiWorksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)( false && 0); // Update file dialog active state when the window is focused on\n    var onWindowFocus = function onWindowFocus() {\n        // Execute the timeout only if the file dialog is opened in the browser\n        if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n            setTimeout(function() {\n                if (inputRef.current) {\n                    var files = inputRef.current.files;\n                    if (!files.length) {\n                        dispatch({\n                            type: \"closeDialog\"\n                        });\n                        onFileDialogCancelCb();\n                    }\n                }\n            }, 300);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        window.addEventListener(\"focus\", onWindowFocus, false);\n        return function() {\n            window.removeEventListener(\"focus\", onWindowFocus, false);\n        };\n    }, [\n        inputRef,\n        isFileDialogActive,\n        onFileDialogCancelCb,\n        fsAccessApiWorksRef\n    ]);\n    var dragTargetsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    var onDocumentDrop = function onDocumentDrop(event) {\n        if (rootRef.current && rootRef.current.contains(event.target)) {\n            // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n            return;\n        }\n        event.preventDefault();\n        dragTargetsRef.current = [];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (preventDropOnDocument) {\n            document.addEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver, false);\n            document.addEventListener(\"drop\", onDocumentDrop, false);\n        }\n        return function() {\n            if (preventDropOnDocument) {\n                document.removeEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver);\n                document.removeEventListener(\"drop\", onDocumentDrop);\n            }\n        };\n    }, [\n        rootRef,\n        preventDropOnDocument\n    ]); // Auto focus the root when autoFocus is true\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!disabled && autoFocus && rootRef.current) {\n            rootRef.current.focus();\n        }\n        return function() {};\n    }, [\n        rootRef,\n        autoFocus,\n        disabled\n    ]);\n    var onErrCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(e) {\n        if (onError) {\n            onError(e);\n        } else {\n            // Let the user know something's gone wrong if they haven't provided the onError cb.\n            console.error(e);\n        }\n    }, [\n        onError\n    ]);\n    var onDragEnterCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n        event.persist();\n        stopPropagation(event);\n        dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [\n            event.target\n        ]);\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n            Promise.resolve(getFilesFromEvent(event)).then(function(files) {\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n                    return;\n                }\n                var fileCount = files.length;\n                var isDragAccept = fileCount > 0 && (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.allFilesAccepted)({\n                    files: files,\n                    accept: acceptAttr,\n                    minSize: minSize,\n                    maxSize: maxSize,\n                    multiple: multiple,\n                    maxFiles: maxFiles,\n                    validator: validator\n                });\n                var isDragReject = fileCount > 0 && !isDragAccept;\n                dispatch({\n                    isDragAccept: isDragAccept,\n                    isDragReject: isDragReject,\n                    isDragActive: true,\n                    type: \"setDraggedFiles\"\n                });\n                if (onDragEnter) {\n                    onDragEnter(event);\n                }\n            }).catch(function(e) {\n                return onErrCb(e);\n            });\n        }\n    }, [\n        getFilesFromEvent,\n        onDragEnter,\n        onErrCb,\n        noDragEventsBubbling,\n        acceptAttr,\n        minSize,\n        maxSize,\n        multiple,\n        maxFiles,\n        validator\n    ]);\n    var onDragOverCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault();\n        event.persist();\n        stopPropagation(event);\n        var hasFiles = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event);\n        if (hasFiles && event.dataTransfer) {\n            try {\n                event.dataTransfer.dropEffect = \"copy\";\n            } catch (_unused) {}\n        /* eslint-disable-line no-empty */ }\n        if (hasFiles && onDragOver) {\n            onDragOver(event);\n        }\n        return false;\n    }, [\n        onDragOver,\n        noDragEventsBubbling\n    ]);\n    var onDragLeaveCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault();\n        event.persist();\n        stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n        var targets = dragTargetsRef.current.filter(function(target) {\n            return rootRef.current && rootRef.current.contains(target);\n        }); // Make sure to remove a target present multiple times only once\n        // (Firefox may fire dragenter/dragleave multiple times on the same element)\n        var targetIdx = targets.indexOf(event.target);\n        if (targetIdx !== -1) {\n            targets.splice(targetIdx, 1);\n        }\n        dragTargetsRef.current = targets;\n        if (targets.length > 0) {\n            return;\n        }\n        dispatch({\n            type: \"setDraggedFiles\",\n            isDragActive: false,\n            isDragAccept: false,\n            isDragReject: false\n        });\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event) && onDragLeave) {\n            onDragLeave(event);\n        }\n    }, [\n        rootRef,\n        onDragLeave,\n        noDragEventsBubbling\n    ]);\n    var setFiles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(files, event) {\n        var acceptedFiles = [];\n        var fileRejections = [];\n        files.forEach(function(file) {\n            var _fileAccepted = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileAccepted)(file, acceptAttr), _fileAccepted2 = _slicedToArray(_fileAccepted, 2), accepted = _fileAccepted2[0], acceptError = _fileAccepted2[1];\n            var _fileMatchSize = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileMatchSize)(file, minSize, maxSize), _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2), sizeMatch = _fileMatchSize2[0], sizeError = _fileMatchSize2[1];\n            var customErrors = validator ? validator(file) : null;\n            if (accepted && sizeMatch && !customErrors) {\n                acceptedFiles.push(file);\n            } else {\n                var errors = [\n                    acceptError,\n                    sizeError\n                ];\n                if (customErrors) {\n                    errors = errors.concat(customErrors);\n                }\n                fileRejections.push({\n                    file: file,\n                    errors: errors.filter(function(e) {\n                        return e;\n                    })\n                });\n            }\n        });\n        if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n            // Reject everything and empty accepted files\n            acceptedFiles.forEach(function(file) {\n                fileRejections.push({\n                    file: file,\n                    errors: [\n                        _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.TOO_MANY_FILES_REJECTION\n                    ]\n                });\n            });\n            acceptedFiles.splice(0);\n        }\n        dispatch({\n            acceptedFiles: acceptedFiles,\n            fileRejections: fileRejections,\n            isDragReject: fileRejections.length > 0,\n            type: \"setFiles\"\n        });\n        if (onDrop) {\n            onDrop(acceptedFiles, fileRejections, event);\n        }\n        if (fileRejections.length > 0 && onDropRejected) {\n            onDropRejected(fileRejections, event);\n        }\n        if (acceptedFiles.length > 0 && onDropAccepted) {\n            onDropAccepted(acceptedFiles, event);\n        }\n    }, [\n        dispatch,\n        multiple,\n        acceptAttr,\n        minSize,\n        maxSize,\n        maxFiles,\n        onDrop,\n        onDropAccepted,\n        onDropRejected,\n        validator\n    ]);\n    var onDropCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n        event.persist();\n        stopPropagation(event);\n        dragTargetsRef.current = [];\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n            Promise.resolve(getFilesFromEvent(event)).then(function(files) {\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n                    return;\n                }\n                setFiles(files, event);\n            }).catch(function(e) {\n                return onErrCb(e);\n            });\n        }\n        dispatch({\n            type: \"reset\"\n        });\n    }, [\n        getFilesFromEvent,\n        setFiles,\n        onErrCb,\n        noDragEventsBubbling\n    ]); // Fn for opening the file dialog programmatically\n    var openFileDialog = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        // No point to use FS access APIs if context is not secure\n        // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n        if (fsAccessApiWorksRef.current) {\n            dispatch({\n                type: \"openDialog\"\n            });\n            onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n            var opts = {\n                multiple: multiple,\n                types: pickerTypes\n            };\n            window.showOpenFilePicker(opts).then(function(handles) {\n                return getFilesFromEvent(handles);\n            }).then(function(files) {\n                setFiles(files, null);\n                dispatch({\n                    type: \"closeDialog\"\n                });\n            }).catch(function(e) {\n                // AbortError means the user canceled\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isAbort)(e)) {\n                    onFileDialogCancelCb(e);\n                    dispatch({\n                        type: \"closeDialog\"\n                    });\n                } else if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isSecurityError)(e)) {\n                    fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n                    // Try using the input\n                    if (inputRef.current) {\n                        inputRef.current.value = null;\n                        inputRef.current.click();\n                    } else {\n                        onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n                    }\n                } else {\n                    onErrCb(e);\n                }\n            });\n            return;\n        }\n        if (inputRef.current) {\n            dispatch({\n                type: \"openDialog\"\n            });\n            onFileDialogOpenCb();\n            inputRef.current.value = null;\n            inputRef.current.click();\n        }\n    }, [\n        dispatch,\n        onFileDialogOpenCb,\n        onFileDialogCancelCb,\n        useFsAccessApi,\n        setFiles,\n        onErrCb,\n        pickerTypes,\n        multiple\n    ]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n    var onKeyDownCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        // Ignore keyboard events bubbling up the DOM tree\n        if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n            return;\n        }\n        if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n            event.preventDefault();\n            openFileDialog();\n        }\n    }, [\n        rootRef,\n        openFileDialog\n    ]); // Update focus state for the dropzone\n    var onFocusCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        dispatch({\n            type: \"focus\"\n        });\n    }, []);\n    var onBlurCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        dispatch({\n            type: \"blur\"\n        });\n    }, []); // Cb to open the file dialog when click occurs on the dropzone\n    var onClickCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (noClick) {\n            return;\n        } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n        // to ensure React can handle state changes\n        // See: https://github.com/react-dropzone/react-dropzone/issues/450\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isIeOrEdge)()) {\n            setTimeout(openFileDialog, 0);\n        } else {\n            openFileDialog();\n        }\n    }, [\n        noClick,\n        openFileDialog\n    ]);\n    var composeHandler = function composeHandler(fn) {\n        return disabled ? null : fn;\n    };\n    var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n        return noKeyboard ? null : composeHandler(fn);\n    };\n    var composeDragHandler = function composeDragHandler(fn) {\n        return noDrag ? null : composeHandler(fn);\n    };\n    var stopPropagation = function stopPropagation(event) {\n        if (noDragEventsBubbling) {\n            event.stopPropagation();\n        }\n    };\n    var getRootProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return function() {\n            var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, _ref2$refKey = _ref2.refKey, refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey, role = _ref2.role, onKeyDown = _ref2.onKeyDown, onFocus = _ref2.onFocus, onBlur = _ref2.onBlur, onClick = _ref2.onClick, onDragEnter = _ref2.onDragEnter, onDragOver = _ref2.onDragOver, onDragLeave = _ref2.onDragLeave, onDrop = _ref2.onDrop, rest = _objectWithoutProperties(_ref2, _excluded3);\n            return _objectSpread(_objectSpread(_defineProperty({\n                onKeyDown: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onKeyDown, onKeyDownCb)),\n                onFocus: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onFocus, onFocusCb)),\n                onBlur: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onBlur, onBlurCb)),\n                onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onClickCb)),\n                onDragEnter: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragEnter, onDragEnterCb)),\n                onDragOver: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragOver, onDragOverCb)),\n                onDragLeave: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragLeave, onDragLeaveCb)),\n                onDrop: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDrop, onDropCb)),\n                role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n            }, refKey, rootRef), !disabled && !noKeyboard ? {\n                tabIndex: 0\n            } : {}), rest);\n        };\n    }, [\n        rootRef,\n        onKeyDownCb,\n        onFocusCb,\n        onBlurCb,\n        onClickCb,\n        onDragEnterCb,\n        onDragOverCb,\n        onDragLeaveCb,\n        onDropCb,\n        noKeyboard,\n        noDrag,\n        disabled\n    ]);\n    var onInputElementClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.stopPropagation();\n    }, []);\n    var getInputProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return function() {\n            var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, _ref3$refKey = _ref3.refKey, refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey, onChange = _ref3.onChange, onClick = _ref3.onClick, rest = _objectWithoutProperties(_ref3, _excluded4);\n            var inputProps = _defineProperty({\n                accept: acceptAttr,\n                multiple: multiple,\n                type: \"file\",\n                style: {\n                    border: 0,\n                    clip: \"rect(0, 0, 0, 0)\",\n                    clipPath: \"inset(50%)\",\n                    height: \"1px\",\n                    margin: \"0 -1px -1px 0\",\n                    overflow: \"hidden\",\n                    padding: 0,\n                    position: \"absolute\",\n                    width: \"1px\",\n                    whiteSpace: \"nowrap\"\n                },\n                onChange: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onChange, onDropCb)),\n                onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onInputElementClick)),\n                tabIndex: -1\n            }, refKey, inputRef);\n            return _objectSpread(_objectSpread({}, inputProps), rest);\n        };\n    }, [\n        inputRef,\n        accept,\n        multiple,\n        onDropCb,\n        disabled\n    ]);\n    return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: isFocused && !disabled,\n        getRootProps: getRootProps,\n        getInputProps: getInputProps,\n        rootRef: rootRef,\n        inputRef: inputRef,\n        open: composeHandler(openFileDialog)\n    });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */ function reducer(state, action) {\n    /* istanbul ignore next */ switch(action.type){\n        case \"focus\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFocused: true\n            });\n        case \"blur\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFocused: false\n            });\n        case \"openDialog\":\n            return _objectSpread(_objectSpread({}, initialState), {}, {\n                isFileDialogActive: true\n            });\n        case \"closeDialog\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFileDialogActive: false\n            });\n        case \"setDraggedFiles\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isDragActive: action.isDragActive,\n                isDragAccept: action.isDragAccept,\n                isDragReject: action.isDragReject\n            });\n        case \"setFiles\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                acceptedFiles: action.acceptedFiles,\n                fileRejections: action.fileRejections,\n                isDragReject: action.isDragReject\n            });\n        case \"reset\":\n            return _objectSpread({}, initialState);\n        default:\n            return state;\n    }\n}\nfunction noop() {}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/utils/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FILE_INVALID_TYPE: () => (/* binding */ FILE_INVALID_TYPE),\n/* harmony export */   FILE_TOO_LARGE: () => (/* binding */ FILE_TOO_LARGE),\n/* harmony export */   FILE_TOO_SMALL: () => (/* binding */ FILE_TOO_SMALL),\n/* harmony export */   TOO_MANY_FILES: () => (/* binding */ TOO_MANY_FILES),\n/* harmony export */   TOO_MANY_FILES_REJECTION: () => (/* binding */ TOO_MANY_FILES_REJECTION),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   canUseFileSystemAccessAPI: () => (/* binding */ canUseFileSystemAccessAPI),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   fileAccepted: () => (/* binding */ fileAccepted),\n/* harmony export */   fileMatchSize: () => (/* binding */ fileMatchSize),\n/* harmony export */   getInvalidTypeRejectionErr: () => (/* binding */ getInvalidTypeRejectionErr),\n/* harmony export */   getTooLargeRejectionErr: () => (/* binding */ getTooLargeRejectionErr),\n/* harmony export */   getTooSmallRejectionErr: () => (/* binding */ getTooSmallRejectionErr),\n/* harmony export */   isAbort: () => (/* binding */ isAbort),\n/* harmony export */   isEvtWithFiles: () => (/* binding */ isEvtWithFiles),\n/* harmony export */   isExt: () => (/* binding */ isExt),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isKindFile: () => (/* binding */ isKindFile),\n/* harmony export */   isMIMEType: () => (/* binding */ isMIMEType),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isSecurityError: () => (/* binding */ isSecurityError),\n/* harmony export */   onDocumentDragOver: () => (/* binding */ onDocumentDragOver),\n/* harmony export */   pickerOptionsFromAccept: () => (/* binding */ pickerOptionsFromAccept)\n/* harmony export */ });\n/* harmony import */ var attr_accept__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! attr-accept */ \"(ssr)/./node_modules/attr-accept/dist/es/index.js\");\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj) {\n        return typeof obj;\n    } : function(obj) {\n        return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\nvar accepts = typeof attr_accept__WEBPACK_IMPORTED_MODULE_0__ === \"function\" ? attr_accept__WEBPACK_IMPORTED_MODULE_0__ : attr_accept__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; // Error codes\nvar FILE_INVALID_TYPE = \"file-invalid-type\";\nvar FILE_TOO_LARGE = \"file-too-large\";\nvar FILE_TOO_SMALL = \"file-too-small\";\nvar TOO_MANY_FILES = \"too-many-files\";\nvar ErrorCode = {\n    FileInvalidType: FILE_INVALID_TYPE,\n    FileTooLarge: FILE_TOO_LARGE,\n    FileTooSmall: FILE_TOO_SMALL,\n    TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */ var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n    var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n    var acceptArr = accept.split(\",\");\n    var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n    return {\n        code: FILE_INVALID_TYPE,\n        message: \"File type must be \".concat(msg)\n    };\n};\nvar getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n    return {\n        code: FILE_TOO_LARGE,\n        message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n    };\n};\nvar getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n    return {\n        code: FILE_TOO_SMALL,\n        message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n    };\n};\nvar TOO_MANY_FILES_REJECTION = {\n    code: TOO_MANY_FILES,\n    message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */ function fileAccepted(file, accept) {\n    var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n    return [\n        isAcceptable,\n        isAcceptable ? null : getInvalidTypeRejectionErr(accept)\n    ];\n}\nfunction fileMatchSize(file, minSize, maxSize) {\n    if (isDefined(file.size)) {\n        if (isDefined(minSize) && isDefined(maxSize)) {\n            if (file.size > maxSize) return [\n                false,\n                getTooLargeRejectionErr(maxSize)\n            ];\n            if (file.size < minSize) return [\n                false,\n                getTooSmallRejectionErr(minSize)\n            ];\n        } else if (isDefined(minSize) && file.size < minSize) return [\n            false,\n            getTooSmallRejectionErr(minSize)\n        ];\n        else if (isDefined(maxSize) && file.size > maxSize) return [\n            false,\n            getTooLargeRejectionErr(maxSize)\n        ];\n    }\n    return [\n        true,\n        null\n    ];\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */ function allFilesAccepted(_ref) {\n    var files = _ref.files, accept = _ref.accept, minSize = _ref.minSize, maxSize = _ref.maxSize, multiple = _ref.multiple, maxFiles = _ref.maxFiles, validator = _ref.validator;\n    if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n        return false;\n    }\n    return files.every(function(file) {\n        var _fileAccepted = fileAccepted(file, accept), _fileAccepted2 = _slicedToArray(_fileAccepted, 1), accepted = _fileAccepted2[0];\n        var _fileMatchSize = fileMatchSize(file, minSize, maxSize), _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1), sizeMatch = _fileMatchSize2[0];\n        var customErrors = validator ? validator(file) : null;\n        return accepted && sizeMatch && !customErrors;\n    });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\nfunction isPropagationStopped(event) {\n    if (typeof event.isPropagationStopped === \"function\") {\n        return event.isPropagationStopped();\n    } else if (typeof event.cancelBubble !== \"undefined\") {\n        return event.cancelBubble;\n    }\n    return false;\n}\nfunction isEvtWithFiles(event) {\n    if (!event.dataTransfer) {\n        return !!event.target && !!event.target.files;\n    } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n    return Array.prototype.some.call(event.dataTransfer.types, function(type) {\n        return type === \"Files\" || type === \"application/x-moz-file\";\n    });\n}\nfunction isKindFile(item) {\n    return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\nfunction onDocumentDragOver(event) {\n    event.preventDefault();\n}\nfunction isIe(userAgent) {\n    return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\nfunction isEdge(userAgent) {\n    return userAgent.indexOf(\"Edge/\") !== -1;\n}\nfunction isIeOrEdge() {\n    var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n    return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */ function composeEventHandlers() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(event) {\n        for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n            args[_key2 - 1] = arguments[_key2];\n        }\n        return fns.some(function(fn) {\n            if (!isPropagationStopped(event) && fn) {\n                fn.apply(void 0, [\n                    event\n                ].concat(args));\n            }\n            return isPropagationStopped(event);\n        });\n    };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */ function canUseFileSystemAccessAPI() {\n    return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */ function pickerOptionsFromAccept(accept) {\n    if (isDefined(accept)) {\n        var acceptForPicker = Object.entries(accept).filter(function(_ref2) {\n            var _ref3 = _slicedToArray(_ref2, 2), mimeType = _ref3[0], ext = _ref3[1];\n            var ok = true;\n            if (!isMIMEType(mimeType)) {\n                console.warn('Skipped \"'.concat(mimeType, '\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.'));\n                ok = false;\n            }\n            if (!Array.isArray(ext) || !ext.every(isExt)) {\n                console.warn('Skipped \"'.concat(mimeType, '\" because an invalid file extension was provided.'));\n                ok = false;\n            }\n            return ok;\n        }).reduce(function(agg, _ref4) {\n            var _ref5 = _slicedToArray(_ref4, 2), mimeType = _ref5[0], ext = _ref5[1];\n            return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n        }, {});\n        return [\n            {\n                // description is required due to https://crbug.com/1264708\n                description: \"Files\",\n                accept: acceptForPicker\n            }\n        ];\n    }\n    return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */ function acceptPropAsAcceptAttr(accept) {\n    if (isDefined(accept)) {\n        return Object.entries(accept).reduce(function(a, _ref6) {\n            var _ref7 = _slicedToArray(_ref6, 2), mimeType = _ref7[0], ext = _ref7[1];\n            return [].concat(_toConsumableArray(a), [\n                mimeType\n            ], _toConsumableArray(ext));\n        }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n        .filter(function(v) {\n            return isMIMEType(v) || isExt(v);\n        }).join(\",\");\n    }\n    return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */ function isAbort(v) {\n    return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */ function isSecurityError(v) {\n    return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */ function isMIMEType(v) {\n    return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */ function isExt(v) {\n    return /^.*\\.[\\w]+$/.test(v);\n} /**\n * @typedef {Object.<string, string[]>} AcceptProp\n */  /**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */  /**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\n");

/***/ })

};
;