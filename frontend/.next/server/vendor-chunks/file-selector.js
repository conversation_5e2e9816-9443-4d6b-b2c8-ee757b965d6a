"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-selector";
exports.ids = ["vendor-chunks/file-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js":
/*!*****************************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file-selector.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* binding */ fromEvent)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file */ \"(ssr)/./node_modules/file-selector/dist/es2015/file.js\");\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    \".DS_Store\",\n    \"Thumbs.db\" // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */ function fromEvent(evt) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        } else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        } else if (Array.isArray(evt) && evt.every((item)=>\"getFile\" in item && typeof item.getFile === \"function\")) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === \"object\" && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map((file)=>(0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        const files = yield Promise.all(handles.map((h)=>h.getFile()));\n        return files.map((file)=>(0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items).filter((item)=>item.kind === \"file\");\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== \"drop\") {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files).map((file)=>(0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter((file)=>FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for(let i = 0; i < items.length; i++){\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== \"function\") {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files)=>[\n            ...acc,\n            ...Array.isArray(files) ? flatten(files) : [\n                files\n            ]\n        ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === \"function\") {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject)=>{\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch)=>(0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n                    if (!batch.length) {\n                        // Done reading directory\n                        try {\n                            const files = yield Promise.all(entries);\n                            resolve(files);\n                        } catch (err) {\n                            reject(err);\n                        }\n                    } else {\n                        const items = Promise.all(batch.map(fromEntry));\n                        entries.push(items);\n                        // Continue reading\n                        readEntries();\n                    }\n                }), (err)=>{\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function*() {\n        return new Promise((resolve, reject)=>{\n            entry.file((file)=>{\n                const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, entry.fullPath);\n                resolve(fwp);\n            }, (err)=>{\n                reject(err);\n            });\n        });\n    });\n} //# sourceMappingURL=file-selector.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file.js":
/*!********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_MIME_TYPES: () => (/* binding */ COMMON_MIME_TYPES),\n/* harmony export */   toFileWithPath: () => (/* binding */ toFileWithPath)\n/* harmony export */ });\nconst COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    [\n        \"1km\",\n        \"application/vnd.1000minds.decision-model+xml\"\n    ],\n    [\n        \"3dml\",\n        \"text/vnd.in3d.3dml\"\n    ],\n    [\n        \"3ds\",\n        \"image/x-3ds\"\n    ],\n    [\n        \"3g2\",\n        \"video/3gpp2\"\n    ],\n    [\n        \"3gp\",\n        \"video/3gp\"\n    ],\n    [\n        \"3gpp\",\n        \"video/3gpp\"\n    ],\n    [\n        \"3mf\",\n        \"model/3mf\"\n    ],\n    [\n        \"7z\",\n        \"application/x-7z-compressed\"\n    ],\n    [\n        \"7zip\",\n        \"application/x-7z-compressed\"\n    ],\n    [\n        \"123\",\n        \"application/vnd.lotus-1-2-3\"\n    ],\n    [\n        \"aab\",\n        \"application/x-authorware-bin\"\n    ],\n    [\n        \"aac\",\n        \"audio/x-acc\"\n    ],\n    [\n        \"aam\",\n        \"application/x-authorware-map\"\n    ],\n    [\n        \"aas\",\n        \"application/x-authorware-seg\"\n    ],\n    [\n        \"abw\",\n        \"application/x-abiword\"\n    ],\n    [\n        \"ac\",\n        \"application/vnd.nokia.n-gage.ac+xml\"\n    ],\n    [\n        \"ac3\",\n        \"audio/ac3\"\n    ],\n    [\n        \"acc\",\n        \"application/vnd.americandynamics.acc\"\n    ],\n    [\n        \"ace\",\n        \"application/x-ace-compressed\"\n    ],\n    [\n        \"acu\",\n        \"application/vnd.acucobol\"\n    ],\n    [\n        \"acutc\",\n        \"application/vnd.acucorp\"\n    ],\n    [\n        \"adp\",\n        \"audio/adpcm\"\n    ],\n    [\n        \"aep\",\n        \"application/vnd.audiograph\"\n    ],\n    [\n        \"afm\",\n        \"application/x-font-type1\"\n    ],\n    [\n        \"afp\",\n        \"application/vnd.ibm.modcap\"\n    ],\n    [\n        \"ahead\",\n        \"application/vnd.ahead.space\"\n    ],\n    [\n        \"ai\",\n        \"application/pdf\"\n    ],\n    [\n        \"aif\",\n        \"audio/x-aiff\"\n    ],\n    [\n        \"aifc\",\n        \"audio/x-aiff\"\n    ],\n    [\n        \"aiff\",\n        \"audio/x-aiff\"\n    ],\n    [\n        \"air\",\n        \"application/vnd.adobe.air-application-installer-package+zip\"\n    ],\n    [\n        \"ait\",\n        \"application/vnd.dvb.ait\"\n    ],\n    [\n        \"ami\",\n        \"application/vnd.amiga.ami\"\n    ],\n    [\n        \"amr\",\n        \"audio/amr\"\n    ],\n    [\n        \"apk\",\n        \"application/vnd.android.package-archive\"\n    ],\n    [\n        \"apng\",\n        \"image/apng\"\n    ],\n    [\n        \"appcache\",\n        \"text/cache-manifest\"\n    ],\n    [\n        \"application\",\n        \"application/x-ms-application\"\n    ],\n    [\n        \"apr\",\n        \"application/vnd.lotus-approach\"\n    ],\n    [\n        \"arc\",\n        \"application/x-freearc\"\n    ],\n    [\n        \"arj\",\n        \"application/x-arj\"\n    ],\n    [\n        \"asc\",\n        \"application/pgp-signature\"\n    ],\n    [\n        \"asf\",\n        \"video/x-ms-asf\"\n    ],\n    [\n        \"asm\",\n        \"text/x-asm\"\n    ],\n    [\n        \"aso\",\n        \"application/vnd.accpac.simply.aso\"\n    ],\n    [\n        \"asx\",\n        \"video/x-ms-asf\"\n    ],\n    [\n        \"atc\",\n        \"application/vnd.acucorp\"\n    ],\n    [\n        \"atom\",\n        \"application/atom+xml\"\n    ],\n    [\n        \"atomcat\",\n        \"application/atomcat+xml\"\n    ],\n    [\n        \"atomdeleted\",\n        \"application/atomdeleted+xml\"\n    ],\n    [\n        \"atomsvc\",\n        \"application/atomsvc+xml\"\n    ],\n    [\n        \"atx\",\n        \"application/vnd.antix.game-component\"\n    ],\n    [\n        \"au\",\n        \"audio/x-au\"\n    ],\n    [\n        \"avi\",\n        \"video/x-msvideo\"\n    ],\n    [\n        \"avif\",\n        \"image/avif\"\n    ],\n    [\n        \"aw\",\n        \"application/applixware\"\n    ],\n    [\n        \"azf\",\n        \"application/vnd.airzip.filesecure.azf\"\n    ],\n    [\n        \"azs\",\n        \"application/vnd.airzip.filesecure.azs\"\n    ],\n    [\n        \"azv\",\n        \"image/vnd.airzip.accelerator.azv\"\n    ],\n    [\n        \"azw\",\n        \"application/vnd.amazon.ebook\"\n    ],\n    [\n        \"b16\",\n        \"image/vnd.pco.b16\"\n    ],\n    [\n        \"bat\",\n        \"application/x-msdownload\"\n    ],\n    [\n        \"bcpio\",\n        \"application/x-bcpio\"\n    ],\n    [\n        \"bdf\",\n        \"application/x-font-bdf\"\n    ],\n    [\n        \"bdm\",\n        \"application/vnd.syncml.dm+wbxml\"\n    ],\n    [\n        \"bdoc\",\n        \"application/x-bdoc\"\n    ],\n    [\n        \"bed\",\n        \"application/vnd.realvnc.bed\"\n    ],\n    [\n        \"bh2\",\n        \"application/vnd.fujitsu.oasysprs\"\n    ],\n    [\n        \"bin\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"blb\",\n        \"application/x-blorb\"\n    ],\n    [\n        \"blorb\",\n        \"application/x-blorb\"\n    ],\n    [\n        \"bmi\",\n        \"application/vnd.bmi\"\n    ],\n    [\n        \"bmml\",\n        \"application/vnd.balsamiq.bmml+xml\"\n    ],\n    [\n        \"bmp\",\n        \"image/bmp\"\n    ],\n    [\n        \"book\",\n        \"application/vnd.framemaker\"\n    ],\n    [\n        \"box\",\n        \"application/vnd.previewsystems.box\"\n    ],\n    [\n        \"boz\",\n        \"application/x-bzip2\"\n    ],\n    [\n        \"bpk\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"bpmn\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"bsp\",\n        \"model/vnd.valve.source.compiled-map\"\n    ],\n    [\n        \"btif\",\n        \"image/prs.btif\"\n    ],\n    [\n        \"buffer\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"bz\",\n        \"application/x-bzip\"\n    ],\n    [\n        \"bz2\",\n        \"application/x-bzip2\"\n    ],\n    [\n        \"c\",\n        \"text/x-c\"\n    ],\n    [\n        \"c4d\",\n        \"application/vnd.clonk.c4group\"\n    ],\n    [\n        \"c4f\",\n        \"application/vnd.clonk.c4group\"\n    ],\n    [\n        \"c4g\",\n        \"application/vnd.clonk.c4group\"\n    ],\n    [\n        \"c4p\",\n        \"application/vnd.clonk.c4group\"\n    ],\n    [\n        \"c4u\",\n        \"application/vnd.clonk.c4group\"\n    ],\n    [\n        \"c11amc\",\n        \"application/vnd.cluetrust.cartomobile-config\"\n    ],\n    [\n        \"c11amz\",\n        \"application/vnd.cluetrust.cartomobile-config-pkg\"\n    ],\n    [\n        \"cab\",\n        \"application/vnd.ms-cab-compressed\"\n    ],\n    [\n        \"caf\",\n        \"audio/x-caf\"\n    ],\n    [\n        \"cap\",\n        \"application/vnd.tcpdump.pcap\"\n    ],\n    [\n        \"car\",\n        \"application/vnd.curl.car\"\n    ],\n    [\n        \"cat\",\n        \"application/vnd.ms-pki.seccat\"\n    ],\n    [\n        \"cb7\",\n        \"application/x-cbr\"\n    ],\n    [\n        \"cba\",\n        \"application/x-cbr\"\n    ],\n    [\n        \"cbr\",\n        \"application/x-cbr\"\n    ],\n    [\n        \"cbt\",\n        \"application/x-cbr\"\n    ],\n    [\n        \"cbz\",\n        \"application/x-cbr\"\n    ],\n    [\n        \"cc\",\n        \"text/x-c\"\n    ],\n    [\n        \"cco\",\n        \"application/x-cocoa\"\n    ],\n    [\n        \"cct\",\n        \"application/x-director\"\n    ],\n    [\n        \"ccxml\",\n        \"application/ccxml+xml\"\n    ],\n    [\n        \"cdbcmsg\",\n        \"application/vnd.contact.cmsg\"\n    ],\n    [\n        \"cda\",\n        \"application/x-cdf\"\n    ],\n    [\n        \"cdf\",\n        \"application/x-netcdf\"\n    ],\n    [\n        \"cdfx\",\n        \"application/cdfx+xml\"\n    ],\n    [\n        \"cdkey\",\n        \"application/vnd.mediastation.cdkey\"\n    ],\n    [\n        \"cdmia\",\n        \"application/cdmi-capability\"\n    ],\n    [\n        \"cdmic\",\n        \"application/cdmi-container\"\n    ],\n    [\n        \"cdmid\",\n        \"application/cdmi-domain\"\n    ],\n    [\n        \"cdmio\",\n        \"application/cdmi-object\"\n    ],\n    [\n        \"cdmiq\",\n        \"application/cdmi-queue\"\n    ],\n    [\n        \"cdr\",\n        \"application/cdr\"\n    ],\n    [\n        \"cdx\",\n        \"chemical/x-cdx\"\n    ],\n    [\n        \"cdxml\",\n        \"application/vnd.chemdraw+xml\"\n    ],\n    [\n        \"cdy\",\n        \"application/vnd.cinderella\"\n    ],\n    [\n        \"cer\",\n        \"application/pkix-cert\"\n    ],\n    [\n        \"cfs\",\n        \"application/x-cfs-compressed\"\n    ],\n    [\n        \"cgm\",\n        \"image/cgm\"\n    ],\n    [\n        \"chat\",\n        \"application/x-chat\"\n    ],\n    [\n        \"chm\",\n        \"application/vnd.ms-htmlhelp\"\n    ],\n    [\n        \"chrt\",\n        \"application/vnd.kde.kchart\"\n    ],\n    [\n        \"cif\",\n        \"chemical/x-cif\"\n    ],\n    [\n        \"cii\",\n        \"application/vnd.anser-web-certificate-issue-initiation\"\n    ],\n    [\n        \"cil\",\n        \"application/vnd.ms-artgalry\"\n    ],\n    [\n        \"cjs\",\n        \"application/node\"\n    ],\n    [\n        \"cla\",\n        \"application/vnd.claymore\"\n    ],\n    [\n        \"class\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"clkk\",\n        \"application/vnd.crick.clicker.keyboard\"\n    ],\n    [\n        \"clkp\",\n        \"application/vnd.crick.clicker.palette\"\n    ],\n    [\n        \"clkt\",\n        \"application/vnd.crick.clicker.template\"\n    ],\n    [\n        \"clkw\",\n        \"application/vnd.crick.clicker.wordbank\"\n    ],\n    [\n        \"clkx\",\n        \"application/vnd.crick.clicker\"\n    ],\n    [\n        \"clp\",\n        \"application/x-msclip\"\n    ],\n    [\n        \"cmc\",\n        \"application/vnd.cosmocaller\"\n    ],\n    [\n        \"cmdf\",\n        \"chemical/x-cmdf\"\n    ],\n    [\n        \"cml\",\n        \"chemical/x-cml\"\n    ],\n    [\n        \"cmp\",\n        \"application/vnd.yellowriver-custom-menu\"\n    ],\n    [\n        \"cmx\",\n        \"image/x-cmx\"\n    ],\n    [\n        \"cod\",\n        \"application/vnd.rim.cod\"\n    ],\n    [\n        \"coffee\",\n        \"text/coffeescript\"\n    ],\n    [\n        \"com\",\n        \"application/x-msdownload\"\n    ],\n    [\n        \"conf\",\n        \"text/plain\"\n    ],\n    [\n        \"cpio\",\n        \"application/x-cpio\"\n    ],\n    [\n        \"cpp\",\n        \"text/x-c\"\n    ],\n    [\n        \"cpt\",\n        \"application/mac-compactpro\"\n    ],\n    [\n        \"crd\",\n        \"application/x-mscardfile\"\n    ],\n    [\n        \"crl\",\n        \"application/pkix-crl\"\n    ],\n    [\n        \"crt\",\n        \"application/x-x509-ca-cert\"\n    ],\n    [\n        \"crx\",\n        \"application/x-chrome-extension\"\n    ],\n    [\n        \"cryptonote\",\n        \"application/vnd.rig.cryptonote\"\n    ],\n    [\n        \"csh\",\n        \"application/x-csh\"\n    ],\n    [\n        \"csl\",\n        \"application/vnd.citationstyles.style+xml\"\n    ],\n    [\n        \"csml\",\n        \"chemical/x-csml\"\n    ],\n    [\n        \"csp\",\n        \"application/vnd.commonspace\"\n    ],\n    [\n        \"csr\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"css\",\n        \"text/css\"\n    ],\n    [\n        \"cst\",\n        \"application/x-director\"\n    ],\n    [\n        \"csv\",\n        \"text/csv\"\n    ],\n    [\n        \"cu\",\n        \"application/cu-seeme\"\n    ],\n    [\n        \"curl\",\n        \"text/vnd.curl\"\n    ],\n    [\n        \"cww\",\n        \"application/prs.cww\"\n    ],\n    [\n        \"cxt\",\n        \"application/x-director\"\n    ],\n    [\n        \"cxx\",\n        \"text/x-c\"\n    ],\n    [\n        \"dae\",\n        \"model/vnd.collada+xml\"\n    ],\n    [\n        \"daf\",\n        \"application/vnd.mobius.daf\"\n    ],\n    [\n        \"dart\",\n        \"application/vnd.dart\"\n    ],\n    [\n        \"dataless\",\n        \"application/vnd.fdsn.seed\"\n    ],\n    [\n        \"davmount\",\n        \"application/davmount+xml\"\n    ],\n    [\n        \"dbf\",\n        \"application/vnd.dbf\"\n    ],\n    [\n        \"dbk\",\n        \"application/docbook+xml\"\n    ],\n    [\n        \"dcr\",\n        \"application/x-director\"\n    ],\n    [\n        \"dcurl\",\n        \"text/vnd.curl.dcurl\"\n    ],\n    [\n        \"dd2\",\n        \"application/vnd.oma.dd2+xml\"\n    ],\n    [\n        \"ddd\",\n        \"application/vnd.fujixerox.ddd\"\n    ],\n    [\n        \"ddf\",\n        \"application/vnd.syncml.dmddf+xml\"\n    ],\n    [\n        \"dds\",\n        \"image/vnd.ms-dds\"\n    ],\n    [\n        \"deb\",\n        \"application/x-debian-package\"\n    ],\n    [\n        \"def\",\n        \"text/plain\"\n    ],\n    [\n        \"deploy\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"der\",\n        \"application/x-x509-ca-cert\"\n    ],\n    [\n        \"dfac\",\n        \"application/vnd.dreamfactory\"\n    ],\n    [\n        \"dgc\",\n        \"application/x-dgc-compressed\"\n    ],\n    [\n        \"dic\",\n        \"text/x-c\"\n    ],\n    [\n        \"dir\",\n        \"application/x-director\"\n    ],\n    [\n        \"dis\",\n        \"application/vnd.mobius.dis\"\n    ],\n    [\n        \"disposition-notification\",\n        \"message/disposition-notification\"\n    ],\n    [\n        \"dist\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"distz\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"djv\",\n        \"image/vnd.djvu\"\n    ],\n    [\n        \"djvu\",\n        \"image/vnd.djvu\"\n    ],\n    [\n        \"dll\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"dmg\",\n        \"application/x-apple-diskimage\"\n    ],\n    [\n        \"dmn\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"dmp\",\n        \"application/vnd.tcpdump.pcap\"\n    ],\n    [\n        \"dms\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"dna\",\n        \"application/vnd.dna\"\n    ],\n    [\n        \"doc\",\n        \"application/msword\"\n    ],\n    [\n        \"docm\",\n        \"application/vnd.ms-word.template.macroEnabled.12\"\n    ],\n    [\n        \"docx\",\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n    ],\n    [\n        \"dot\",\n        \"application/msword\"\n    ],\n    [\n        \"dotm\",\n        \"application/vnd.ms-word.template.macroEnabled.12\"\n    ],\n    [\n        \"dotx\",\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.template\"\n    ],\n    [\n        \"dp\",\n        \"application/vnd.osgi.dp\"\n    ],\n    [\n        \"dpg\",\n        \"application/vnd.dpgraph\"\n    ],\n    [\n        \"dra\",\n        \"audio/vnd.dra\"\n    ],\n    [\n        \"drle\",\n        \"image/dicom-rle\"\n    ],\n    [\n        \"dsc\",\n        \"text/prs.lines.tag\"\n    ],\n    [\n        \"dssc\",\n        \"application/dssc+der\"\n    ],\n    [\n        \"dtb\",\n        \"application/x-dtbook+xml\"\n    ],\n    [\n        \"dtd\",\n        \"application/xml-dtd\"\n    ],\n    [\n        \"dts\",\n        \"audio/vnd.dts\"\n    ],\n    [\n        \"dtshd\",\n        \"audio/vnd.dts.hd\"\n    ],\n    [\n        \"dump\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"dvb\",\n        \"video/vnd.dvb.file\"\n    ],\n    [\n        \"dvi\",\n        \"application/x-dvi\"\n    ],\n    [\n        \"dwd\",\n        \"application/atsc-dwd+xml\"\n    ],\n    [\n        \"dwf\",\n        \"model/vnd.dwf\"\n    ],\n    [\n        \"dwg\",\n        \"image/vnd.dwg\"\n    ],\n    [\n        \"dxf\",\n        \"image/vnd.dxf\"\n    ],\n    [\n        \"dxp\",\n        \"application/vnd.spotfire.dxp\"\n    ],\n    [\n        \"dxr\",\n        \"application/x-director\"\n    ],\n    [\n        \"ear\",\n        \"application/java-archive\"\n    ],\n    [\n        \"ecelp4800\",\n        \"audio/vnd.nuera.ecelp4800\"\n    ],\n    [\n        \"ecelp7470\",\n        \"audio/vnd.nuera.ecelp7470\"\n    ],\n    [\n        \"ecelp9600\",\n        \"audio/vnd.nuera.ecelp9600\"\n    ],\n    [\n        \"ecma\",\n        \"application/ecmascript\"\n    ],\n    [\n        \"edm\",\n        \"application/vnd.novadigm.edm\"\n    ],\n    [\n        \"edx\",\n        \"application/vnd.novadigm.edx\"\n    ],\n    [\n        \"efif\",\n        \"application/vnd.picsel\"\n    ],\n    [\n        \"ei6\",\n        \"application/vnd.pg.osasli\"\n    ],\n    [\n        \"elc\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"emf\",\n        \"image/emf\"\n    ],\n    [\n        \"eml\",\n        \"message/rfc822\"\n    ],\n    [\n        \"emma\",\n        \"application/emma+xml\"\n    ],\n    [\n        \"emotionml\",\n        \"application/emotionml+xml\"\n    ],\n    [\n        \"emz\",\n        \"application/x-msmetafile\"\n    ],\n    [\n        \"eol\",\n        \"audio/vnd.digital-winds\"\n    ],\n    [\n        \"eot\",\n        \"application/vnd.ms-fontobject\"\n    ],\n    [\n        \"eps\",\n        \"application/postscript\"\n    ],\n    [\n        \"epub\",\n        \"application/epub+zip\"\n    ],\n    [\n        \"es\",\n        \"application/ecmascript\"\n    ],\n    [\n        \"es3\",\n        \"application/vnd.eszigno3+xml\"\n    ],\n    [\n        \"esa\",\n        \"application/vnd.osgi.subsystem\"\n    ],\n    [\n        \"esf\",\n        \"application/vnd.epson.esf\"\n    ],\n    [\n        \"et3\",\n        \"application/vnd.eszigno3+xml\"\n    ],\n    [\n        \"etx\",\n        \"text/x-setext\"\n    ],\n    [\n        \"eva\",\n        \"application/x-eva\"\n    ],\n    [\n        \"evy\",\n        \"application/x-envoy\"\n    ],\n    [\n        \"exe\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"exi\",\n        \"application/exi\"\n    ],\n    [\n        \"exp\",\n        \"application/express\"\n    ],\n    [\n        \"exr\",\n        \"image/aces\"\n    ],\n    [\n        \"ext\",\n        \"application/vnd.novadigm.ext\"\n    ],\n    [\n        \"ez\",\n        \"application/andrew-inset\"\n    ],\n    [\n        \"ez2\",\n        \"application/vnd.ezpix-album\"\n    ],\n    [\n        \"ez3\",\n        \"application/vnd.ezpix-package\"\n    ],\n    [\n        \"f\",\n        \"text/x-fortran\"\n    ],\n    [\n        \"f4v\",\n        \"video/mp4\"\n    ],\n    [\n        \"f77\",\n        \"text/x-fortran\"\n    ],\n    [\n        \"f90\",\n        \"text/x-fortran\"\n    ],\n    [\n        \"fbs\",\n        \"image/vnd.fastbidsheet\"\n    ],\n    [\n        \"fcdt\",\n        \"application/vnd.adobe.formscentral.fcdt\"\n    ],\n    [\n        \"fcs\",\n        \"application/vnd.isac.fcs\"\n    ],\n    [\n        \"fdf\",\n        \"application/vnd.fdf\"\n    ],\n    [\n        \"fdt\",\n        \"application/fdt+xml\"\n    ],\n    [\n        \"fe_launch\",\n        \"application/vnd.denovo.fcselayout-link\"\n    ],\n    [\n        \"fg5\",\n        \"application/vnd.fujitsu.oasysgp\"\n    ],\n    [\n        \"fgd\",\n        \"application/x-director\"\n    ],\n    [\n        \"fh\",\n        \"image/x-freehand\"\n    ],\n    [\n        \"fh4\",\n        \"image/x-freehand\"\n    ],\n    [\n        \"fh5\",\n        \"image/x-freehand\"\n    ],\n    [\n        \"fh7\",\n        \"image/x-freehand\"\n    ],\n    [\n        \"fhc\",\n        \"image/x-freehand\"\n    ],\n    [\n        \"fig\",\n        \"application/x-xfig\"\n    ],\n    [\n        \"fits\",\n        \"image/fits\"\n    ],\n    [\n        \"flac\",\n        \"audio/x-flac\"\n    ],\n    [\n        \"fli\",\n        \"video/x-fli\"\n    ],\n    [\n        \"flo\",\n        \"application/vnd.micrografx.flo\"\n    ],\n    [\n        \"flv\",\n        \"video/x-flv\"\n    ],\n    [\n        \"flw\",\n        \"application/vnd.kde.kivio\"\n    ],\n    [\n        \"flx\",\n        \"text/vnd.fmi.flexstor\"\n    ],\n    [\n        \"fly\",\n        \"text/vnd.fly\"\n    ],\n    [\n        \"fm\",\n        \"application/vnd.framemaker\"\n    ],\n    [\n        \"fnc\",\n        \"application/vnd.frogans.fnc\"\n    ],\n    [\n        \"fo\",\n        \"application/vnd.software602.filler.form+xml\"\n    ],\n    [\n        \"for\",\n        \"text/x-fortran\"\n    ],\n    [\n        \"fpx\",\n        \"image/vnd.fpx\"\n    ],\n    [\n        \"frame\",\n        \"application/vnd.framemaker\"\n    ],\n    [\n        \"fsc\",\n        \"application/vnd.fsc.weblaunch\"\n    ],\n    [\n        \"fst\",\n        \"image/vnd.fst\"\n    ],\n    [\n        \"ftc\",\n        \"application/vnd.fluxtime.clip\"\n    ],\n    [\n        \"fti\",\n        \"application/vnd.anser-web-funds-transfer-initiation\"\n    ],\n    [\n        \"fvt\",\n        \"video/vnd.fvt\"\n    ],\n    [\n        \"fxp\",\n        \"application/vnd.adobe.fxp\"\n    ],\n    [\n        \"fxpl\",\n        \"application/vnd.adobe.fxp\"\n    ],\n    [\n        \"fzs\",\n        \"application/vnd.fuzzysheet\"\n    ],\n    [\n        \"g2w\",\n        \"application/vnd.geoplan\"\n    ],\n    [\n        \"g3\",\n        \"image/g3fax\"\n    ],\n    [\n        \"g3w\",\n        \"application/vnd.geospace\"\n    ],\n    [\n        \"gac\",\n        \"application/vnd.groove-account\"\n    ],\n    [\n        \"gam\",\n        \"application/x-tads\"\n    ],\n    [\n        \"gbr\",\n        \"application/rpki-ghostbusters\"\n    ],\n    [\n        \"gca\",\n        \"application/x-gca-compressed\"\n    ],\n    [\n        \"gdl\",\n        \"model/vnd.gdl\"\n    ],\n    [\n        \"gdoc\",\n        \"application/vnd.google-apps.document\"\n    ],\n    [\n        \"geo\",\n        \"application/vnd.dynageo\"\n    ],\n    [\n        \"geojson\",\n        \"application/geo+json\"\n    ],\n    [\n        \"gex\",\n        \"application/vnd.geometry-explorer\"\n    ],\n    [\n        \"ggb\",\n        \"application/vnd.geogebra.file\"\n    ],\n    [\n        \"ggt\",\n        \"application/vnd.geogebra.tool\"\n    ],\n    [\n        \"ghf\",\n        \"application/vnd.groove-help\"\n    ],\n    [\n        \"gif\",\n        \"image/gif\"\n    ],\n    [\n        \"gim\",\n        \"application/vnd.groove-identity-message\"\n    ],\n    [\n        \"glb\",\n        \"model/gltf-binary\"\n    ],\n    [\n        \"gltf\",\n        \"model/gltf+json\"\n    ],\n    [\n        \"gml\",\n        \"application/gml+xml\"\n    ],\n    [\n        \"gmx\",\n        \"application/vnd.gmx\"\n    ],\n    [\n        \"gnumeric\",\n        \"application/x-gnumeric\"\n    ],\n    [\n        \"gpg\",\n        \"application/gpg-keys\"\n    ],\n    [\n        \"gph\",\n        \"application/vnd.flographit\"\n    ],\n    [\n        \"gpx\",\n        \"application/gpx+xml\"\n    ],\n    [\n        \"gqf\",\n        \"application/vnd.grafeq\"\n    ],\n    [\n        \"gqs\",\n        \"application/vnd.grafeq\"\n    ],\n    [\n        \"gram\",\n        \"application/srgs\"\n    ],\n    [\n        \"gramps\",\n        \"application/x-gramps-xml\"\n    ],\n    [\n        \"gre\",\n        \"application/vnd.geometry-explorer\"\n    ],\n    [\n        \"grv\",\n        \"application/vnd.groove-injector\"\n    ],\n    [\n        \"grxml\",\n        \"application/srgs+xml\"\n    ],\n    [\n        \"gsf\",\n        \"application/x-font-ghostscript\"\n    ],\n    [\n        \"gsheet\",\n        \"application/vnd.google-apps.spreadsheet\"\n    ],\n    [\n        \"gslides\",\n        \"application/vnd.google-apps.presentation\"\n    ],\n    [\n        \"gtar\",\n        \"application/x-gtar\"\n    ],\n    [\n        \"gtm\",\n        \"application/vnd.groove-tool-message\"\n    ],\n    [\n        \"gtw\",\n        \"model/vnd.gtw\"\n    ],\n    [\n        \"gv\",\n        \"text/vnd.graphviz\"\n    ],\n    [\n        \"gxf\",\n        \"application/gxf\"\n    ],\n    [\n        \"gxt\",\n        \"application/vnd.geonext\"\n    ],\n    [\n        \"gz\",\n        \"application/gzip\"\n    ],\n    [\n        \"gzip\",\n        \"application/gzip\"\n    ],\n    [\n        \"h\",\n        \"text/x-c\"\n    ],\n    [\n        \"h261\",\n        \"video/h261\"\n    ],\n    [\n        \"h263\",\n        \"video/h263\"\n    ],\n    [\n        \"h264\",\n        \"video/h264\"\n    ],\n    [\n        \"hal\",\n        \"application/vnd.hal+xml\"\n    ],\n    [\n        \"hbci\",\n        \"application/vnd.hbci\"\n    ],\n    [\n        \"hbs\",\n        \"text/x-handlebars-template\"\n    ],\n    [\n        \"hdd\",\n        \"application/x-virtualbox-hdd\"\n    ],\n    [\n        \"hdf\",\n        \"application/x-hdf\"\n    ],\n    [\n        \"heic\",\n        \"image/heic\"\n    ],\n    [\n        \"heics\",\n        \"image/heic-sequence\"\n    ],\n    [\n        \"heif\",\n        \"image/heif\"\n    ],\n    [\n        \"heifs\",\n        \"image/heif-sequence\"\n    ],\n    [\n        \"hej2\",\n        \"image/hej2k\"\n    ],\n    [\n        \"held\",\n        \"application/atsc-held+xml\"\n    ],\n    [\n        \"hh\",\n        \"text/x-c\"\n    ],\n    [\n        \"hjson\",\n        \"application/hjson\"\n    ],\n    [\n        \"hlp\",\n        \"application/winhlp\"\n    ],\n    [\n        \"hpgl\",\n        \"application/vnd.hp-hpgl\"\n    ],\n    [\n        \"hpid\",\n        \"application/vnd.hp-hpid\"\n    ],\n    [\n        \"hps\",\n        \"application/vnd.hp-hps\"\n    ],\n    [\n        \"hqx\",\n        \"application/mac-binhex40\"\n    ],\n    [\n        \"hsj2\",\n        \"image/hsj2\"\n    ],\n    [\n        \"htc\",\n        \"text/x-component\"\n    ],\n    [\n        \"htke\",\n        \"application/vnd.kenameaapp\"\n    ],\n    [\n        \"htm\",\n        \"text/html\"\n    ],\n    [\n        \"html\",\n        \"text/html\"\n    ],\n    [\n        \"hvd\",\n        \"application/vnd.yamaha.hv-dic\"\n    ],\n    [\n        \"hvp\",\n        \"application/vnd.yamaha.hv-voice\"\n    ],\n    [\n        \"hvs\",\n        \"application/vnd.yamaha.hv-script\"\n    ],\n    [\n        \"i2g\",\n        \"application/vnd.intergeo\"\n    ],\n    [\n        \"icc\",\n        \"application/vnd.iccprofile\"\n    ],\n    [\n        \"ice\",\n        \"x-conference/x-cooltalk\"\n    ],\n    [\n        \"icm\",\n        \"application/vnd.iccprofile\"\n    ],\n    [\n        \"ico\",\n        \"image/x-icon\"\n    ],\n    [\n        \"ics\",\n        \"text/calendar\"\n    ],\n    [\n        \"ief\",\n        \"image/ief\"\n    ],\n    [\n        \"ifb\",\n        \"text/calendar\"\n    ],\n    [\n        \"ifm\",\n        \"application/vnd.shana.informed.formdata\"\n    ],\n    [\n        \"iges\",\n        \"model/iges\"\n    ],\n    [\n        \"igl\",\n        \"application/vnd.igloader\"\n    ],\n    [\n        \"igm\",\n        \"application/vnd.insors.igm\"\n    ],\n    [\n        \"igs\",\n        \"model/iges\"\n    ],\n    [\n        \"igx\",\n        \"application/vnd.micrografx.igx\"\n    ],\n    [\n        \"iif\",\n        \"application/vnd.shana.informed.interchange\"\n    ],\n    [\n        \"img\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"imp\",\n        \"application/vnd.accpac.simply.imp\"\n    ],\n    [\n        \"ims\",\n        \"application/vnd.ms-ims\"\n    ],\n    [\n        \"in\",\n        \"text/plain\"\n    ],\n    [\n        \"ini\",\n        \"text/plain\"\n    ],\n    [\n        \"ink\",\n        \"application/inkml+xml\"\n    ],\n    [\n        \"inkml\",\n        \"application/inkml+xml\"\n    ],\n    [\n        \"install\",\n        \"application/x-install-instructions\"\n    ],\n    [\n        \"iota\",\n        \"application/vnd.astraea-software.iota\"\n    ],\n    [\n        \"ipfix\",\n        \"application/ipfix\"\n    ],\n    [\n        \"ipk\",\n        \"application/vnd.shana.informed.package\"\n    ],\n    [\n        \"irm\",\n        \"application/vnd.ibm.rights-management\"\n    ],\n    [\n        \"irp\",\n        \"application/vnd.irepository.package+xml\"\n    ],\n    [\n        \"iso\",\n        \"application/x-iso9660-image\"\n    ],\n    [\n        \"itp\",\n        \"application/vnd.shana.informed.formtemplate\"\n    ],\n    [\n        \"its\",\n        \"application/its+xml\"\n    ],\n    [\n        \"ivp\",\n        \"application/vnd.immervision-ivp\"\n    ],\n    [\n        \"ivu\",\n        \"application/vnd.immervision-ivu\"\n    ],\n    [\n        \"jad\",\n        \"text/vnd.sun.j2me.app-descriptor\"\n    ],\n    [\n        \"jade\",\n        \"text/jade\"\n    ],\n    [\n        \"jam\",\n        \"application/vnd.jam\"\n    ],\n    [\n        \"jar\",\n        \"application/java-archive\"\n    ],\n    [\n        \"jardiff\",\n        \"application/x-java-archive-diff\"\n    ],\n    [\n        \"java\",\n        \"text/x-java-source\"\n    ],\n    [\n        \"jhc\",\n        \"image/jphc\"\n    ],\n    [\n        \"jisp\",\n        \"application/vnd.jisp\"\n    ],\n    [\n        \"jls\",\n        \"image/jls\"\n    ],\n    [\n        \"jlt\",\n        \"application/vnd.hp-jlyt\"\n    ],\n    [\n        \"jng\",\n        \"image/x-jng\"\n    ],\n    [\n        \"jnlp\",\n        \"application/x-java-jnlp-file\"\n    ],\n    [\n        \"joda\",\n        \"application/vnd.joost.joda-archive\"\n    ],\n    [\n        \"jp2\",\n        \"image/jp2\"\n    ],\n    [\n        \"jpe\",\n        \"image/jpeg\"\n    ],\n    [\n        \"jpeg\",\n        \"image/jpeg\"\n    ],\n    [\n        \"jpf\",\n        \"image/jpx\"\n    ],\n    [\n        \"jpg\",\n        \"image/jpeg\"\n    ],\n    [\n        \"jpg2\",\n        \"image/jp2\"\n    ],\n    [\n        \"jpgm\",\n        \"video/jpm\"\n    ],\n    [\n        \"jpgv\",\n        \"video/jpeg\"\n    ],\n    [\n        \"jph\",\n        \"image/jph\"\n    ],\n    [\n        \"jpm\",\n        \"video/jpm\"\n    ],\n    [\n        \"jpx\",\n        \"image/jpx\"\n    ],\n    [\n        \"js\",\n        \"application/javascript\"\n    ],\n    [\n        \"json\",\n        \"application/json\"\n    ],\n    [\n        \"json5\",\n        \"application/json5\"\n    ],\n    [\n        \"jsonld\",\n        \"application/ld+json\"\n    ],\n    // https://jsonlines.org/\n    [\n        \"jsonl\",\n        \"application/jsonl\"\n    ],\n    [\n        \"jsonml\",\n        \"application/jsonml+json\"\n    ],\n    [\n        \"jsx\",\n        \"text/jsx\"\n    ],\n    [\n        \"jxr\",\n        \"image/jxr\"\n    ],\n    [\n        \"jxra\",\n        \"image/jxra\"\n    ],\n    [\n        \"jxrs\",\n        \"image/jxrs\"\n    ],\n    [\n        \"jxs\",\n        \"image/jxs\"\n    ],\n    [\n        \"jxsc\",\n        \"image/jxsc\"\n    ],\n    [\n        \"jxsi\",\n        \"image/jxsi\"\n    ],\n    [\n        \"jxss\",\n        \"image/jxss\"\n    ],\n    [\n        \"kar\",\n        \"audio/midi\"\n    ],\n    [\n        \"karbon\",\n        \"application/vnd.kde.karbon\"\n    ],\n    [\n        \"kdb\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"kdbx\",\n        \"application/x-keepass2\"\n    ],\n    [\n        \"key\",\n        \"application/x-iwork-keynote-sffkey\"\n    ],\n    [\n        \"kfo\",\n        \"application/vnd.kde.kformula\"\n    ],\n    [\n        \"kia\",\n        \"application/vnd.kidspiration\"\n    ],\n    [\n        \"kml\",\n        \"application/vnd.google-earth.kml+xml\"\n    ],\n    [\n        \"kmz\",\n        \"application/vnd.google-earth.kmz\"\n    ],\n    [\n        \"kne\",\n        \"application/vnd.kinar\"\n    ],\n    [\n        \"knp\",\n        \"application/vnd.kinar\"\n    ],\n    [\n        \"kon\",\n        \"application/vnd.kde.kontour\"\n    ],\n    [\n        \"kpr\",\n        \"application/vnd.kde.kpresenter\"\n    ],\n    [\n        \"kpt\",\n        \"application/vnd.kde.kpresenter\"\n    ],\n    [\n        \"kpxx\",\n        \"application/vnd.ds-keypoint\"\n    ],\n    [\n        \"ksp\",\n        \"application/vnd.kde.kspread\"\n    ],\n    [\n        \"ktr\",\n        \"application/vnd.kahootz\"\n    ],\n    [\n        \"ktx\",\n        \"image/ktx\"\n    ],\n    [\n        \"ktx2\",\n        \"image/ktx2\"\n    ],\n    [\n        \"ktz\",\n        \"application/vnd.kahootz\"\n    ],\n    [\n        \"kwd\",\n        \"application/vnd.kde.kword\"\n    ],\n    [\n        \"kwt\",\n        \"application/vnd.kde.kword\"\n    ],\n    [\n        \"lasxml\",\n        \"application/vnd.las.las+xml\"\n    ],\n    [\n        \"latex\",\n        \"application/x-latex\"\n    ],\n    [\n        \"lbd\",\n        \"application/vnd.llamagraphics.life-balance.desktop\"\n    ],\n    [\n        \"lbe\",\n        \"application/vnd.llamagraphics.life-balance.exchange+xml\"\n    ],\n    [\n        \"les\",\n        \"application/vnd.hhe.lesson-player\"\n    ],\n    [\n        \"less\",\n        \"text/less\"\n    ],\n    [\n        \"lgr\",\n        \"application/lgr+xml\"\n    ],\n    [\n        \"lha\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"link66\",\n        \"application/vnd.route66.link66+xml\"\n    ],\n    [\n        \"list\",\n        \"text/plain\"\n    ],\n    [\n        \"list3820\",\n        \"application/vnd.ibm.modcap\"\n    ],\n    [\n        \"listafp\",\n        \"application/vnd.ibm.modcap\"\n    ],\n    [\n        \"litcoffee\",\n        \"text/coffeescript\"\n    ],\n    [\n        \"lnk\",\n        \"application/x-ms-shortcut\"\n    ],\n    [\n        \"log\",\n        \"text/plain\"\n    ],\n    [\n        \"lostxml\",\n        \"application/lost+xml\"\n    ],\n    [\n        \"lrf\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"lrm\",\n        \"application/vnd.ms-lrm\"\n    ],\n    [\n        \"ltf\",\n        \"application/vnd.frogans.ltf\"\n    ],\n    [\n        \"lua\",\n        \"text/x-lua\"\n    ],\n    [\n        \"luac\",\n        \"application/x-lua-bytecode\"\n    ],\n    [\n        \"lvp\",\n        \"audio/vnd.lucent.voice\"\n    ],\n    [\n        \"lwp\",\n        \"application/vnd.lotus-wordpro\"\n    ],\n    [\n        \"lzh\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"m1v\",\n        \"video/mpeg\"\n    ],\n    [\n        \"m2a\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"m2v\",\n        \"video/mpeg\"\n    ],\n    [\n        \"m3a\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"m3u\",\n        \"text/plain\"\n    ],\n    [\n        \"m3u8\",\n        \"application/vnd.apple.mpegurl\"\n    ],\n    [\n        \"m4a\",\n        \"audio/x-m4a\"\n    ],\n    [\n        \"m4p\",\n        \"application/mp4\"\n    ],\n    [\n        \"m4s\",\n        \"video/iso.segment\"\n    ],\n    [\n        \"m4u\",\n        \"application/vnd.mpegurl\"\n    ],\n    [\n        \"m4v\",\n        \"video/x-m4v\"\n    ],\n    [\n        \"m13\",\n        \"application/x-msmediaview\"\n    ],\n    [\n        \"m14\",\n        \"application/x-msmediaview\"\n    ],\n    [\n        \"m21\",\n        \"application/mp21\"\n    ],\n    [\n        \"ma\",\n        \"application/mathematica\"\n    ],\n    [\n        \"mads\",\n        \"application/mads+xml\"\n    ],\n    [\n        \"maei\",\n        \"application/mmt-aei+xml\"\n    ],\n    [\n        \"mag\",\n        \"application/vnd.ecowin.chart\"\n    ],\n    [\n        \"maker\",\n        \"application/vnd.framemaker\"\n    ],\n    [\n        \"man\",\n        \"text/troff\"\n    ],\n    [\n        \"manifest\",\n        \"text/cache-manifest\"\n    ],\n    [\n        \"map\",\n        \"application/json\"\n    ],\n    [\n        \"mar\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"markdown\",\n        \"text/markdown\"\n    ],\n    [\n        \"mathml\",\n        \"application/mathml+xml\"\n    ],\n    [\n        \"mb\",\n        \"application/mathematica\"\n    ],\n    [\n        \"mbk\",\n        \"application/vnd.mobius.mbk\"\n    ],\n    [\n        \"mbox\",\n        \"application/mbox\"\n    ],\n    [\n        \"mc1\",\n        \"application/vnd.medcalcdata\"\n    ],\n    [\n        \"mcd\",\n        \"application/vnd.mcd\"\n    ],\n    [\n        \"mcurl\",\n        \"text/vnd.curl.mcurl\"\n    ],\n    [\n        \"md\",\n        \"text/markdown\"\n    ],\n    [\n        \"mdb\",\n        \"application/x-msaccess\"\n    ],\n    [\n        \"mdi\",\n        \"image/vnd.ms-modi\"\n    ],\n    [\n        \"mdx\",\n        \"text/mdx\"\n    ],\n    [\n        \"me\",\n        \"text/troff\"\n    ],\n    [\n        \"mesh\",\n        \"model/mesh\"\n    ],\n    [\n        \"meta4\",\n        \"application/metalink4+xml\"\n    ],\n    [\n        \"metalink\",\n        \"application/metalink+xml\"\n    ],\n    [\n        \"mets\",\n        \"application/mets+xml\"\n    ],\n    [\n        \"mfm\",\n        \"application/vnd.mfmp\"\n    ],\n    [\n        \"mft\",\n        \"application/rpki-manifest\"\n    ],\n    [\n        \"mgp\",\n        \"application/vnd.osgeo.mapguide.package\"\n    ],\n    [\n        \"mgz\",\n        \"application/vnd.proteus.magazine\"\n    ],\n    [\n        \"mid\",\n        \"audio/midi\"\n    ],\n    [\n        \"midi\",\n        \"audio/midi\"\n    ],\n    [\n        \"mie\",\n        \"application/x-mie\"\n    ],\n    [\n        \"mif\",\n        \"application/vnd.mif\"\n    ],\n    [\n        \"mime\",\n        \"message/rfc822\"\n    ],\n    [\n        \"mj2\",\n        \"video/mj2\"\n    ],\n    [\n        \"mjp2\",\n        \"video/mj2\"\n    ],\n    [\n        \"mjs\",\n        \"application/javascript\"\n    ],\n    [\n        \"mk3d\",\n        \"video/x-matroska\"\n    ],\n    [\n        \"mka\",\n        \"audio/x-matroska\"\n    ],\n    [\n        \"mkd\",\n        \"text/x-markdown\"\n    ],\n    [\n        \"mks\",\n        \"video/x-matroska\"\n    ],\n    [\n        \"mkv\",\n        \"video/x-matroska\"\n    ],\n    [\n        \"mlp\",\n        \"application/vnd.dolby.mlp\"\n    ],\n    [\n        \"mmd\",\n        \"application/vnd.chipnuts.karaoke-mmd\"\n    ],\n    [\n        \"mmf\",\n        \"application/vnd.smaf\"\n    ],\n    [\n        \"mml\",\n        \"text/mathml\"\n    ],\n    [\n        \"mmr\",\n        \"image/vnd.fujixerox.edmics-mmr\"\n    ],\n    [\n        \"mng\",\n        \"video/x-mng\"\n    ],\n    [\n        \"mny\",\n        \"application/x-msmoney\"\n    ],\n    [\n        \"mobi\",\n        \"application/x-mobipocket-ebook\"\n    ],\n    [\n        \"mods\",\n        \"application/mods+xml\"\n    ],\n    [\n        \"mov\",\n        \"video/quicktime\"\n    ],\n    [\n        \"movie\",\n        \"video/x-sgi-movie\"\n    ],\n    [\n        \"mp2\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"mp2a\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"mp3\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"mp4\",\n        \"video/mp4\"\n    ],\n    [\n        \"mp4a\",\n        \"audio/mp4\"\n    ],\n    [\n        \"mp4s\",\n        \"application/mp4\"\n    ],\n    [\n        \"mp4v\",\n        \"video/mp4\"\n    ],\n    [\n        \"mp21\",\n        \"application/mp21\"\n    ],\n    [\n        \"mpc\",\n        \"application/vnd.mophun.certificate\"\n    ],\n    [\n        \"mpd\",\n        \"application/dash+xml\"\n    ],\n    [\n        \"mpe\",\n        \"video/mpeg\"\n    ],\n    [\n        \"mpeg\",\n        \"video/mpeg\"\n    ],\n    [\n        \"mpg\",\n        \"video/mpeg\"\n    ],\n    [\n        \"mpg4\",\n        \"video/mp4\"\n    ],\n    [\n        \"mpga\",\n        \"audio/mpeg\"\n    ],\n    [\n        \"mpkg\",\n        \"application/vnd.apple.installer+xml\"\n    ],\n    [\n        \"mpm\",\n        \"application/vnd.blueice.multipass\"\n    ],\n    [\n        \"mpn\",\n        \"application/vnd.mophun.application\"\n    ],\n    [\n        \"mpp\",\n        \"application/vnd.ms-project\"\n    ],\n    [\n        \"mpt\",\n        \"application/vnd.ms-project\"\n    ],\n    [\n        \"mpy\",\n        \"application/vnd.ibm.minipay\"\n    ],\n    [\n        \"mqy\",\n        \"application/vnd.mobius.mqy\"\n    ],\n    [\n        \"mrc\",\n        \"application/marc\"\n    ],\n    [\n        \"mrcx\",\n        \"application/marcxml+xml\"\n    ],\n    [\n        \"ms\",\n        \"text/troff\"\n    ],\n    [\n        \"mscml\",\n        \"application/mediaservercontrol+xml\"\n    ],\n    [\n        \"mseed\",\n        \"application/vnd.fdsn.mseed\"\n    ],\n    [\n        \"mseq\",\n        \"application/vnd.mseq\"\n    ],\n    [\n        \"msf\",\n        \"application/vnd.epson.msf\"\n    ],\n    [\n        \"msg\",\n        \"application/vnd.ms-outlook\"\n    ],\n    [\n        \"msh\",\n        \"model/mesh\"\n    ],\n    [\n        \"msi\",\n        \"application/x-msdownload\"\n    ],\n    [\n        \"msl\",\n        \"application/vnd.mobius.msl\"\n    ],\n    [\n        \"msm\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"msp\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"msty\",\n        \"application/vnd.muvee.style\"\n    ],\n    [\n        \"mtl\",\n        \"model/mtl\"\n    ],\n    [\n        \"mts\",\n        \"model/vnd.mts\"\n    ],\n    [\n        \"mus\",\n        \"application/vnd.musician\"\n    ],\n    [\n        \"musd\",\n        \"application/mmt-usd+xml\"\n    ],\n    [\n        \"musicxml\",\n        \"application/vnd.recordare.musicxml+xml\"\n    ],\n    [\n        \"mvb\",\n        \"application/x-msmediaview\"\n    ],\n    [\n        \"mvt\",\n        \"application/vnd.mapbox-vector-tile\"\n    ],\n    [\n        \"mwf\",\n        \"application/vnd.mfer\"\n    ],\n    [\n        \"mxf\",\n        \"application/mxf\"\n    ],\n    [\n        \"mxl\",\n        \"application/vnd.recordare.musicxml\"\n    ],\n    [\n        \"mxmf\",\n        \"audio/mobile-xmf\"\n    ],\n    [\n        \"mxml\",\n        \"application/xv+xml\"\n    ],\n    [\n        \"mxs\",\n        \"application/vnd.triscape.mxs\"\n    ],\n    [\n        \"mxu\",\n        \"video/vnd.mpegurl\"\n    ],\n    [\n        \"n-gage\",\n        \"application/vnd.nokia.n-gage.symbian.install\"\n    ],\n    [\n        \"n3\",\n        \"text/n3\"\n    ],\n    [\n        \"nb\",\n        \"application/mathematica\"\n    ],\n    [\n        \"nbp\",\n        \"application/vnd.wolfram.player\"\n    ],\n    [\n        \"nc\",\n        \"application/x-netcdf\"\n    ],\n    [\n        \"ncx\",\n        \"application/x-dtbncx+xml\"\n    ],\n    [\n        \"nfo\",\n        \"text/x-nfo\"\n    ],\n    [\n        \"ngdat\",\n        \"application/vnd.nokia.n-gage.data\"\n    ],\n    [\n        \"nitf\",\n        \"application/vnd.nitf\"\n    ],\n    [\n        \"nlu\",\n        \"application/vnd.neurolanguage.nlu\"\n    ],\n    [\n        \"nml\",\n        \"application/vnd.enliven\"\n    ],\n    [\n        \"nnd\",\n        \"application/vnd.noblenet-directory\"\n    ],\n    [\n        \"nns\",\n        \"application/vnd.noblenet-sealer\"\n    ],\n    [\n        \"nnw\",\n        \"application/vnd.noblenet-web\"\n    ],\n    [\n        \"npx\",\n        \"image/vnd.net-fpx\"\n    ],\n    [\n        \"nq\",\n        \"application/n-quads\"\n    ],\n    [\n        \"nsc\",\n        \"application/x-conference\"\n    ],\n    [\n        \"nsf\",\n        \"application/vnd.lotus-notes\"\n    ],\n    [\n        \"nt\",\n        \"application/n-triples\"\n    ],\n    [\n        \"ntf\",\n        \"application/vnd.nitf\"\n    ],\n    [\n        \"numbers\",\n        \"application/x-iwork-numbers-sffnumbers\"\n    ],\n    [\n        \"nzb\",\n        \"application/x-nzb\"\n    ],\n    [\n        \"oa2\",\n        \"application/vnd.fujitsu.oasys2\"\n    ],\n    [\n        \"oa3\",\n        \"application/vnd.fujitsu.oasys3\"\n    ],\n    [\n        \"oas\",\n        \"application/vnd.fujitsu.oasys\"\n    ],\n    [\n        \"obd\",\n        \"application/x-msbinder\"\n    ],\n    [\n        \"obgx\",\n        \"application/vnd.openblox.game+xml\"\n    ],\n    [\n        \"obj\",\n        \"model/obj\"\n    ],\n    [\n        \"oda\",\n        \"application/oda\"\n    ],\n    [\n        \"odb\",\n        \"application/vnd.oasis.opendocument.database\"\n    ],\n    [\n        \"odc\",\n        \"application/vnd.oasis.opendocument.chart\"\n    ],\n    [\n        \"odf\",\n        \"application/vnd.oasis.opendocument.formula\"\n    ],\n    [\n        \"odft\",\n        \"application/vnd.oasis.opendocument.formula-template\"\n    ],\n    [\n        \"odg\",\n        \"application/vnd.oasis.opendocument.graphics\"\n    ],\n    [\n        \"odi\",\n        \"application/vnd.oasis.opendocument.image\"\n    ],\n    [\n        \"odm\",\n        \"application/vnd.oasis.opendocument.text-master\"\n    ],\n    [\n        \"odp\",\n        \"application/vnd.oasis.opendocument.presentation\"\n    ],\n    [\n        \"ods\",\n        \"application/vnd.oasis.opendocument.spreadsheet\"\n    ],\n    [\n        \"odt\",\n        \"application/vnd.oasis.opendocument.text\"\n    ],\n    [\n        \"oga\",\n        \"audio/ogg\"\n    ],\n    [\n        \"ogex\",\n        \"model/vnd.opengex\"\n    ],\n    [\n        \"ogg\",\n        \"audio/ogg\"\n    ],\n    [\n        \"ogv\",\n        \"video/ogg\"\n    ],\n    [\n        \"ogx\",\n        \"application/ogg\"\n    ],\n    [\n        \"omdoc\",\n        \"application/omdoc+xml\"\n    ],\n    [\n        \"onepkg\",\n        \"application/onenote\"\n    ],\n    [\n        \"onetmp\",\n        \"application/onenote\"\n    ],\n    [\n        \"onetoc\",\n        \"application/onenote\"\n    ],\n    [\n        \"onetoc2\",\n        \"application/onenote\"\n    ],\n    [\n        \"opf\",\n        \"application/oebps-package+xml\"\n    ],\n    [\n        \"opml\",\n        \"text/x-opml\"\n    ],\n    [\n        \"oprc\",\n        \"application/vnd.palm\"\n    ],\n    [\n        \"opus\",\n        \"audio/ogg\"\n    ],\n    [\n        \"org\",\n        \"text/x-org\"\n    ],\n    [\n        \"osf\",\n        \"application/vnd.yamaha.openscoreformat\"\n    ],\n    [\n        \"osfpvg\",\n        \"application/vnd.yamaha.openscoreformat.osfpvg+xml\"\n    ],\n    [\n        \"osm\",\n        \"application/vnd.openstreetmap.data+xml\"\n    ],\n    [\n        \"otc\",\n        \"application/vnd.oasis.opendocument.chart-template\"\n    ],\n    [\n        \"otf\",\n        \"font/otf\"\n    ],\n    [\n        \"otg\",\n        \"application/vnd.oasis.opendocument.graphics-template\"\n    ],\n    [\n        \"oth\",\n        \"application/vnd.oasis.opendocument.text-web\"\n    ],\n    [\n        \"oti\",\n        \"application/vnd.oasis.opendocument.image-template\"\n    ],\n    [\n        \"otp\",\n        \"application/vnd.oasis.opendocument.presentation-template\"\n    ],\n    [\n        \"ots\",\n        \"application/vnd.oasis.opendocument.spreadsheet-template\"\n    ],\n    [\n        \"ott\",\n        \"application/vnd.oasis.opendocument.text-template\"\n    ],\n    [\n        \"ova\",\n        \"application/x-virtualbox-ova\"\n    ],\n    [\n        \"ovf\",\n        \"application/x-virtualbox-ovf\"\n    ],\n    [\n        \"owl\",\n        \"application/rdf+xml\"\n    ],\n    [\n        \"oxps\",\n        \"application/oxps\"\n    ],\n    [\n        \"oxt\",\n        \"application/vnd.openofficeorg.extension\"\n    ],\n    [\n        \"p\",\n        \"text/x-pascal\"\n    ],\n    [\n        \"p7a\",\n        \"application/x-pkcs7-signature\"\n    ],\n    [\n        \"p7b\",\n        \"application/x-pkcs7-certificates\"\n    ],\n    [\n        \"p7c\",\n        \"application/pkcs7-mime\"\n    ],\n    [\n        \"p7m\",\n        \"application/pkcs7-mime\"\n    ],\n    [\n        \"p7r\",\n        \"application/x-pkcs7-certreqresp\"\n    ],\n    [\n        \"p7s\",\n        \"application/pkcs7-signature\"\n    ],\n    [\n        \"p8\",\n        \"application/pkcs8\"\n    ],\n    [\n        \"p10\",\n        \"application/x-pkcs10\"\n    ],\n    [\n        \"p12\",\n        \"application/x-pkcs12\"\n    ],\n    [\n        \"pac\",\n        \"application/x-ns-proxy-autoconfig\"\n    ],\n    [\n        \"pages\",\n        \"application/x-iwork-pages-sffpages\"\n    ],\n    [\n        \"pas\",\n        \"text/x-pascal\"\n    ],\n    [\n        \"paw\",\n        \"application/vnd.pawaafile\"\n    ],\n    [\n        \"pbd\",\n        \"application/vnd.powerbuilder6\"\n    ],\n    [\n        \"pbm\",\n        \"image/x-portable-bitmap\"\n    ],\n    [\n        \"pcap\",\n        \"application/vnd.tcpdump.pcap\"\n    ],\n    [\n        \"pcf\",\n        \"application/x-font-pcf\"\n    ],\n    [\n        \"pcl\",\n        \"application/vnd.hp-pcl\"\n    ],\n    [\n        \"pclxl\",\n        \"application/vnd.hp-pclxl\"\n    ],\n    [\n        \"pct\",\n        \"image/x-pict\"\n    ],\n    [\n        \"pcurl\",\n        \"application/vnd.curl.pcurl\"\n    ],\n    [\n        \"pcx\",\n        \"image/x-pcx\"\n    ],\n    [\n        \"pdb\",\n        \"application/x-pilot\"\n    ],\n    [\n        \"pde\",\n        \"text/x-processing\"\n    ],\n    [\n        \"pdf\",\n        \"application/pdf\"\n    ],\n    [\n        \"pem\",\n        \"application/x-x509-user-cert\"\n    ],\n    [\n        \"pfa\",\n        \"application/x-font-type1\"\n    ],\n    [\n        \"pfb\",\n        \"application/x-font-type1\"\n    ],\n    [\n        \"pfm\",\n        \"application/x-font-type1\"\n    ],\n    [\n        \"pfr\",\n        \"application/font-tdpfr\"\n    ],\n    [\n        \"pfx\",\n        \"application/x-pkcs12\"\n    ],\n    [\n        \"pgm\",\n        \"image/x-portable-graymap\"\n    ],\n    [\n        \"pgn\",\n        \"application/x-chess-pgn\"\n    ],\n    [\n        \"pgp\",\n        \"application/pgp\"\n    ],\n    [\n        \"php\",\n        \"application/x-httpd-php\"\n    ],\n    [\n        \"php3\",\n        \"application/x-httpd-php\"\n    ],\n    [\n        \"php4\",\n        \"application/x-httpd-php\"\n    ],\n    [\n        \"phps\",\n        \"application/x-httpd-php-source\"\n    ],\n    [\n        \"phtml\",\n        \"application/x-httpd-php\"\n    ],\n    [\n        \"pic\",\n        \"image/x-pict\"\n    ],\n    [\n        \"pkg\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"pki\",\n        \"application/pkixcmp\"\n    ],\n    [\n        \"pkipath\",\n        \"application/pkix-pkipath\"\n    ],\n    [\n        \"pkpass\",\n        \"application/vnd.apple.pkpass\"\n    ],\n    [\n        \"pl\",\n        \"application/x-perl\"\n    ],\n    [\n        \"plb\",\n        \"application/vnd.3gpp.pic-bw-large\"\n    ],\n    [\n        \"plc\",\n        \"application/vnd.mobius.plc\"\n    ],\n    [\n        \"plf\",\n        \"application/vnd.pocketlearn\"\n    ],\n    [\n        \"pls\",\n        \"application/pls+xml\"\n    ],\n    [\n        \"pm\",\n        \"application/x-perl\"\n    ],\n    [\n        \"pml\",\n        \"application/vnd.ctc-posml\"\n    ],\n    [\n        \"png\",\n        \"image/png\"\n    ],\n    [\n        \"pnm\",\n        \"image/x-portable-anymap\"\n    ],\n    [\n        \"portpkg\",\n        \"application/vnd.macports.portpkg\"\n    ],\n    [\n        \"pot\",\n        \"application/vnd.ms-powerpoint\"\n    ],\n    [\n        \"potm\",\n        \"application/vnd.ms-powerpoint.presentation.macroEnabled.12\"\n    ],\n    [\n        \"potx\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.template\"\n    ],\n    [\n        \"ppa\",\n        \"application/vnd.ms-powerpoint\"\n    ],\n    [\n        \"ppam\",\n        \"application/vnd.ms-powerpoint.addin.macroEnabled.12\"\n    ],\n    [\n        \"ppd\",\n        \"application/vnd.cups-ppd\"\n    ],\n    [\n        \"ppm\",\n        \"image/x-portable-pixmap\"\n    ],\n    [\n        \"pps\",\n        \"application/vnd.ms-powerpoint\"\n    ],\n    [\n        \"ppsm\",\n        \"application/vnd.ms-powerpoint.slideshow.macroEnabled.12\"\n    ],\n    [\n        \"ppsx\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.slideshow\"\n    ],\n    [\n        \"ppt\",\n        \"application/powerpoint\"\n    ],\n    [\n        \"pptm\",\n        \"application/vnd.ms-powerpoint.presentation.macroEnabled.12\"\n    ],\n    [\n        \"pptx\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.presentation\"\n    ],\n    [\n        \"pqa\",\n        \"application/vnd.palm\"\n    ],\n    [\n        \"prc\",\n        \"application/x-pilot\"\n    ],\n    [\n        \"pre\",\n        \"application/vnd.lotus-freelance\"\n    ],\n    [\n        \"prf\",\n        \"application/pics-rules\"\n    ],\n    [\n        \"provx\",\n        \"application/provenance+xml\"\n    ],\n    [\n        \"ps\",\n        \"application/postscript\"\n    ],\n    [\n        \"psb\",\n        \"application/vnd.3gpp.pic-bw-small\"\n    ],\n    [\n        \"psd\",\n        \"application/x-photoshop\"\n    ],\n    [\n        \"psf\",\n        \"application/x-font-linux-psf\"\n    ],\n    [\n        \"pskcxml\",\n        \"application/pskc+xml\"\n    ],\n    [\n        \"pti\",\n        \"image/prs.pti\"\n    ],\n    [\n        \"ptid\",\n        \"application/vnd.pvi.ptid1\"\n    ],\n    [\n        \"pub\",\n        \"application/x-mspublisher\"\n    ],\n    [\n        \"pvb\",\n        \"application/vnd.3gpp.pic-bw-var\"\n    ],\n    [\n        \"pwn\",\n        \"application/vnd.3m.post-it-notes\"\n    ],\n    [\n        \"pya\",\n        \"audio/vnd.ms-playready.media.pya\"\n    ],\n    [\n        \"pyv\",\n        \"video/vnd.ms-playready.media.pyv\"\n    ],\n    [\n        \"qam\",\n        \"application/vnd.epson.quickanime\"\n    ],\n    [\n        \"qbo\",\n        \"application/vnd.intu.qbo\"\n    ],\n    [\n        \"qfx\",\n        \"application/vnd.intu.qfx\"\n    ],\n    [\n        \"qps\",\n        \"application/vnd.publishare-delta-tree\"\n    ],\n    [\n        \"qt\",\n        \"video/quicktime\"\n    ],\n    [\n        \"qwd\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"qwt\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"qxb\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"qxd\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"qxl\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"qxt\",\n        \"application/vnd.quark.quarkxpress\"\n    ],\n    [\n        \"ra\",\n        \"audio/x-realaudio\"\n    ],\n    [\n        \"ram\",\n        \"audio/x-pn-realaudio\"\n    ],\n    [\n        \"raml\",\n        \"application/raml+yaml\"\n    ],\n    [\n        \"rapd\",\n        \"application/route-apd+xml\"\n    ],\n    [\n        \"rar\",\n        \"application/x-rar\"\n    ],\n    [\n        \"ras\",\n        \"image/x-cmu-raster\"\n    ],\n    [\n        \"rcprofile\",\n        \"application/vnd.ipunplugged.rcprofile\"\n    ],\n    [\n        \"rdf\",\n        \"application/rdf+xml\"\n    ],\n    [\n        \"rdz\",\n        \"application/vnd.data-vision.rdz\"\n    ],\n    [\n        \"relo\",\n        \"application/p2p-overlay+xml\"\n    ],\n    [\n        \"rep\",\n        \"application/vnd.businessobjects\"\n    ],\n    [\n        \"res\",\n        \"application/x-dtbresource+xml\"\n    ],\n    [\n        \"rgb\",\n        \"image/x-rgb\"\n    ],\n    [\n        \"rif\",\n        \"application/reginfo+xml\"\n    ],\n    [\n        \"rip\",\n        \"audio/vnd.rip\"\n    ],\n    [\n        \"ris\",\n        \"application/x-research-info-systems\"\n    ],\n    [\n        \"rl\",\n        \"application/resource-lists+xml\"\n    ],\n    [\n        \"rlc\",\n        \"image/vnd.fujixerox.edmics-rlc\"\n    ],\n    [\n        \"rld\",\n        \"application/resource-lists-diff+xml\"\n    ],\n    [\n        \"rm\",\n        \"audio/x-pn-realaudio\"\n    ],\n    [\n        \"rmi\",\n        \"audio/midi\"\n    ],\n    [\n        \"rmp\",\n        \"audio/x-pn-realaudio-plugin\"\n    ],\n    [\n        \"rms\",\n        \"application/vnd.jcp.javame.midlet-rms\"\n    ],\n    [\n        \"rmvb\",\n        \"application/vnd.rn-realmedia-vbr\"\n    ],\n    [\n        \"rnc\",\n        \"application/relax-ng-compact-syntax\"\n    ],\n    [\n        \"rng\",\n        \"application/xml\"\n    ],\n    [\n        \"roa\",\n        \"application/rpki-roa\"\n    ],\n    [\n        \"roff\",\n        \"text/troff\"\n    ],\n    [\n        \"rp9\",\n        \"application/vnd.cloanto.rp9\"\n    ],\n    [\n        \"rpm\",\n        \"audio/x-pn-realaudio-plugin\"\n    ],\n    [\n        \"rpss\",\n        \"application/vnd.nokia.radio-presets\"\n    ],\n    [\n        \"rpst\",\n        \"application/vnd.nokia.radio-preset\"\n    ],\n    [\n        \"rq\",\n        \"application/sparql-query\"\n    ],\n    [\n        \"rs\",\n        \"application/rls-services+xml\"\n    ],\n    [\n        \"rsa\",\n        \"application/x-pkcs7\"\n    ],\n    [\n        \"rsat\",\n        \"application/atsc-rsat+xml\"\n    ],\n    [\n        \"rsd\",\n        \"application/rsd+xml\"\n    ],\n    [\n        \"rsheet\",\n        \"application/urc-ressheet+xml\"\n    ],\n    [\n        \"rss\",\n        \"application/rss+xml\"\n    ],\n    [\n        \"rtf\",\n        \"text/rtf\"\n    ],\n    [\n        \"rtx\",\n        \"text/richtext\"\n    ],\n    [\n        \"run\",\n        \"application/x-makeself\"\n    ],\n    [\n        \"rusd\",\n        \"application/route-usd+xml\"\n    ],\n    [\n        \"rv\",\n        \"video/vnd.rn-realvideo\"\n    ],\n    [\n        \"s\",\n        \"text/x-asm\"\n    ],\n    [\n        \"s3m\",\n        \"audio/s3m\"\n    ],\n    [\n        \"saf\",\n        \"application/vnd.yamaha.smaf-audio\"\n    ],\n    [\n        \"sass\",\n        \"text/x-sass\"\n    ],\n    [\n        \"sbml\",\n        \"application/sbml+xml\"\n    ],\n    [\n        \"sc\",\n        \"application/vnd.ibm.secure-container\"\n    ],\n    [\n        \"scd\",\n        \"application/x-msschedule\"\n    ],\n    [\n        \"scm\",\n        \"application/vnd.lotus-screencam\"\n    ],\n    [\n        \"scq\",\n        \"application/scvp-cv-request\"\n    ],\n    [\n        \"scs\",\n        \"application/scvp-cv-response\"\n    ],\n    [\n        \"scss\",\n        \"text/x-scss\"\n    ],\n    [\n        \"scurl\",\n        \"text/vnd.curl.scurl\"\n    ],\n    [\n        \"sda\",\n        \"application/vnd.stardivision.draw\"\n    ],\n    [\n        \"sdc\",\n        \"application/vnd.stardivision.calc\"\n    ],\n    [\n        \"sdd\",\n        \"application/vnd.stardivision.impress\"\n    ],\n    [\n        \"sdkd\",\n        \"application/vnd.solent.sdkm+xml\"\n    ],\n    [\n        \"sdkm\",\n        \"application/vnd.solent.sdkm+xml\"\n    ],\n    [\n        \"sdp\",\n        \"application/sdp\"\n    ],\n    [\n        \"sdw\",\n        \"application/vnd.stardivision.writer\"\n    ],\n    [\n        \"sea\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"see\",\n        \"application/vnd.seemail\"\n    ],\n    [\n        \"seed\",\n        \"application/vnd.fdsn.seed\"\n    ],\n    [\n        \"sema\",\n        \"application/vnd.sema\"\n    ],\n    [\n        \"semd\",\n        \"application/vnd.semd\"\n    ],\n    [\n        \"semf\",\n        \"application/vnd.semf\"\n    ],\n    [\n        \"senmlx\",\n        \"application/senml+xml\"\n    ],\n    [\n        \"sensmlx\",\n        \"application/sensml+xml\"\n    ],\n    [\n        \"ser\",\n        \"application/java-serialized-object\"\n    ],\n    [\n        \"setpay\",\n        \"application/set-payment-initiation\"\n    ],\n    [\n        \"setreg\",\n        \"application/set-registration-initiation\"\n    ],\n    [\n        \"sfd-hdstx\",\n        \"application/vnd.hydrostatix.sof-data\"\n    ],\n    [\n        \"sfs\",\n        \"application/vnd.spotfire.sfs\"\n    ],\n    [\n        \"sfv\",\n        \"text/x-sfv\"\n    ],\n    [\n        \"sgi\",\n        \"image/sgi\"\n    ],\n    [\n        \"sgl\",\n        \"application/vnd.stardivision.writer-global\"\n    ],\n    [\n        \"sgm\",\n        \"text/sgml\"\n    ],\n    [\n        \"sgml\",\n        \"text/sgml\"\n    ],\n    [\n        \"sh\",\n        \"application/x-sh\"\n    ],\n    [\n        \"shar\",\n        \"application/x-shar\"\n    ],\n    [\n        \"shex\",\n        \"text/shex\"\n    ],\n    [\n        \"shf\",\n        \"application/shf+xml\"\n    ],\n    [\n        \"shtml\",\n        \"text/html\"\n    ],\n    [\n        \"sid\",\n        \"image/x-mrsid-image\"\n    ],\n    [\n        \"sieve\",\n        \"application/sieve\"\n    ],\n    [\n        \"sig\",\n        \"application/pgp-signature\"\n    ],\n    [\n        \"sil\",\n        \"audio/silk\"\n    ],\n    [\n        \"silo\",\n        \"model/mesh\"\n    ],\n    [\n        \"sis\",\n        \"application/vnd.symbian.install\"\n    ],\n    [\n        \"sisx\",\n        \"application/vnd.symbian.install\"\n    ],\n    [\n        \"sit\",\n        \"application/x-stuffit\"\n    ],\n    [\n        \"sitx\",\n        \"application/x-stuffitx\"\n    ],\n    [\n        \"siv\",\n        \"application/sieve\"\n    ],\n    [\n        \"skd\",\n        \"application/vnd.koan\"\n    ],\n    [\n        \"skm\",\n        \"application/vnd.koan\"\n    ],\n    [\n        \"skp\",\n        \"application/vnd.koan\"\n    ],\n    [\n        \"skt\",\n        \"application/vnd.koan\"\n    ],\n    [\n        \"sldm\",\n        \"application/vnd.ms-powerpoint.slide.macroenabled.12\"\n    ],\n    [\n        \"sldx\",\n        \"application/vnd.openxmlformats-officedocument.presentationml.slide\"\n    ],\n    [\n        \"slim\",\n        \"text/slim\"\n    ],\n    [\n        \"slm\",\n        \"text/slim\"\n    ],\n    [\n        \"sls\",\n        \"application/route-s-tsid+xml\"\n    ],\n    [\n        \"slt\",\n        \"application/vnd.epson.salt\"\n    ],\n    [\n        \"sm\",\n        \"application/vnd.stepmania.stepchart\"\n    ],\n    [\n        \"smf\",\n        \"application/vnd.stardivision.math\"\n    ],\n    [\n        \"smi\",\n        \"application/smil\"\n    ],\n    [\n        \"smil\",\n        \"application/smil\"\n    ],\n    [\n        \"smv\",\n        \"video/x-smv\"\n    ],\n    [\n        \"smzip\",\n        \"application/vnd.stepmania.package\"\n    ],\n    [\n        \"snd\",\n        \"audio/basic\"\n    ],\n    [\n        \"snf\",\n        \"application/x-font-snf\"\n    ],\n    [\n        \"so\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"spc\",\n        \"application/x-pkcs7-certificates\"\n    ],\n    [\n        \"spdx\",\n        \"text/spdx\"\n    ],\n    [\n        \"spf\",\n        \"application/vnd.yamaha.smaf-phrase\"\n    ],\n    [\n        \"spl\",\n        \"application/x-futuresplash\"\n    ],\n    [\n        \"spot\",\n        \"text/vnd.in3d.spot\"\n    ],\n    [\n        \"spp\",\n        \"application/scvp-vp-response\"\n    ],\n    [\n        \"spq\",\n        \"application/scvp-vp-request\"\n    ],\n    [\n        \"spx\",\n        \"audio/ogg\"\n    ],\n    [\n        \"sql\",\n        \"application/x-sql\"\n    ],\n    [\n        \"src\",\n        \"application/x-wais-source\"\n    ],\n    [\n        \"srt\",\n        \"application/x-subrip\"\n    ],\n    [\n        \"sru\",\n        \"application/sru+xml\"\n    ],\n    [\n        \"srx\",\n        \"application/sparql-results+xml\"\n    ],\n    [\n        \"ssdl\",\n        \"application/ssdl+xml\"\n    ],\n    [\n        \"sse\",\n        \"application/vnd.kodak-descriptor\"\n    ],\n    [\n        \"ssf\",\n        \"application/vnd.epson.ssf\"\n    ],\n    [\n        \"ssml\",\n        \"application/ssml+xml\"\n    ],\n    [\n        \"sst\",\n        \"application/octet-stream\"\n    ],\n    [\n        \"st\",\n        \"application/vnd.sailingtracker.track\"\n    ],\n    [\n        \"stc\",\n        \"application/vnd.sun.xml.calc.template\"\n    ],\n    [\n        \"std\",\n        \"application/vnd.sun.xml.draw.template\"\n    ],\n    [\n        \"stf\",\n        \"application/vnd.wt.stf\"\n    ],\n    [\n        \"sti\",\n        \"application/vnd.sun.xml.impress.template\"\n    ],\n    [\n        \"stk\",\n        \"application/hyperstudio\"\n    ],\n    [\n        \"stl\",\n        \"model/stl\"\n    ],\n    [\n        \"stpx\",\n        \"model/step+xml\"\n    ],\n    [\n        \"stpxz\",\n        \"model/step-xml+zip\"\n    ],\n    [\n        \"stpz\",\n        \"model/step+zip\"\n    ],\n    [\n        \"str\",\n        \"application/vnd.pg.format\"\n    ],\n    [\n        \"stw\",\n        \"application/vnd.sun.xml.writer.template\"\n    ],\n    [\n        \"styl\",\n        \"text/stylus\"\n    ],\n    [\n        \"stylus\",\n        \"text/stylus\"\n    ],\n    [\n        \"sub\",\n        \"text/vnd.dvb.subtitle\"\n    ],\n    [\n        \"sus\",\n        \"application/vnd.sus-calendar\"\n    ],\n    [\n        \"susp\",\n        \"application/vnd.sus-calendar\"\n    ],\n    [\n        \"sv4cpio\",\n        \"application/x-sv4cpio\"\n    ],\n    [\n        \"sv4crc\",\n        \"application/x-sv4crc\"\n    ],\n    [\n        \"svc\",\n        \"application/vnd.dvb.service\"\n    ],\n    [\n        \"svd\",\n        \"application/vnd.svd\"\n    ],\n    [\n        \"svg\",\n        \"image/svg+xml\"\n    ],\n    [\n        \"svgz\",\n        \"image/svg+xml\"\n    ],\n    [\n        \"swa\",\n        \"application/x-director\"\n    ],\n    [\n        \"swf\",\n        \"application/x-shockwave-flash\"\n    ],\n    [\n        \"swi\",\n        \"application/vnd.aristanetworks.swi\"\n    ],\n    [\n        \"swidtag\",\n        \"application/swid+xml\"\n    ],\n    [\n        \"sxc\",\n        \"application/vnd.sun.xml.calc\"\n    ],\n    [\n        \"sxd\",\n        \"application/vnd.sun.xml.draw\"\n    ],\n    [\n        \"sxg\",\n        \"application/vnd.sun.xml.writer.global\"\n    ],\n    [\n        \"sxi\",\n        \"application/vnd.sun.xml.impress\"\n    ],\n    [\n        \"sxm\",\n        \"application/vnd.sun.xml.math\"\n    ],\n    [\n        \"sxw\",\n        \"application/vnd.sun.xml.writer\"\n    ],\n    [\n        \"t\",\n        \"text/troff\"\n    ],\n    [\n        \"t3\",\n        \"application/x-t3vm-image\"\n    ],\n    [\n        \"t38\",\n        \"image/t38\"\n    ],\n    [\n        \"taglet\",\n        \"application/vnd.mynfc\"\n    ],\n    [\n        \"tao\",\n        \"application/vnd.tao.intent-module-archive\"\n    ],\n    [\n        \"tap\",\n        \"image/vnd.tencent.tap\"\n    ],\n    [\n        \"tar\",\n        \"application/x-tar\"\n    ],\n    [\n        \"tcap\",\n        \"application/vnd.3gpp2.tcap\"\n    ],\n    [\n        \"tcl\",\n        \"application/x-tcl\"\n    ],\n    [\n        \"td\",\n        \"application/urc-targetdesc+xml\"\n    ],\n    [\n        \"teacher\",\n        \"application/vnd.smart.teacher\"\n    ],\n    [\n        \"tei\",\n        \"application/tei+xml\"\n    ],\n    [\n        \"teicorpus\",\n        \"application/tei+xml\"\n    ],\n    [\n        \"tex\",\n        \"application/x-tex\"\n    ],\n    [\n        \"texi\",\n        \"application/x-texinfo\"\n    ],\n    [\n        \"texinfo\",\n        \"application/x-texinfo\"\n    ],\n    [\n        \"text\",\n        \"text/plain\"\n    ],\n    [\n        \"tfi\",\n        \"application/thraud+xml\"\n    ],\n    [\n        \"tfm\",\n        \"application/x-tex-tfm\"\n    ],\n    [\n        \"tfx\",\n        \"image/tiff-fx\"\n    ],\n    [\n        \"tga\",\n        \"image/x-tga\"\n    ],\n    [\n        \"tgz\",\n        \"application/x-tar\"\n    ],\n    [\n        \"thmx\",\n        \"application/vnd.ms-officetheme\"\n    ],\n    [\n        \"tif\",\n        \"image/tiff\"\n    ],\n    [\n        \"tiff\",\n        \"image/tiff\"\n    ],\n    [\n        \"tk\",\n        \"application/x-tcl\"\n    ],\n    [\n        \"tmo\",\n        \"application/vnd.tmobile-livetv\"\n    ],\n    [\n        \"toml\",\n        \"application/toml\"\n    ],\n    [\n        \"torrent\",\n        \"application/x-bittorrent\"\n    ],\n    [\n        \"tpl\",\n        \"application/vnd.groove-tool-template\"\n    ],\n    [\n        \"tpt\",\n        \"application/vnd.trid.tpt\"\n    ],\n    [\n        \"tr\",\n        \"text/troff\"\n    ],\n    [\n        \"tra\",\n        \"application/vnd.trueapp\"\n    ],\n    [\n        \"trig\",\n        \"application/trig\"\n    ],\n    [\n        \"trm\",\n        \"application/x-msterminal\"\n    ],\n    [\n        \"ts\",\n        \"video/mp2t\"\n    ],\n    [\n        \"tsd\",\n        \"application/timestamped-data\"\n    ],\n    [\n        \"tsv\",\n        \"text/tab-separated-values\"\n    ],\n    [\n        \"ttc\",\n        \"font/collection\"\n    ],\n    [\n        \"ttf\",\n        \"font/ttf\"\n    ],\n    [\n        \"ttl\",\n        \"text/turtle\"\n    ],\n    [\n        \"ttml\",\n        \"application/ttml+xml\"\n    ],\n    [\n        \"twd\",\n        \"application/vnd.simtech-mindmapper\"\n    ],\n    [\n        \"twds\",\n        \"application/vnd.simtech-mindmapper\"\n    ],\n    [\n        \"txd\",\n        \"application/vnd.genomatix.tuxedo\"\n    ],\n    [\n        \"txf\",\n        \"application/vnd.mobius.txf\"\n    ],\n    [\n        \"txt\",\n        \"text/plain\"\n    ],\n    [\n        \"u8dsn\",\n        \"message/global-delivery-status\"\n    ],\n    [\n        \"u8hdr\",\n        \"message/global-headers\"\n    ],\n    [\n        \"u8mdn\",\n        \"message/global-disposition-notification\"\n    ],\n    [\n        \"u8msg\",\n        \"message/global\"\n    ],\n    [\n        \"u32\",\n        \"application/x-authorware-bin\"\n    ],\n    [\n        \"ubj\",\n        \"application/ubjson\"\n    ],\n    [\n        \"udeb\",\n        \"application/x-debian-package\"\n    ],\n    [\n        \"ufd\",\n        \"application/vnd.ufdl\"\n    ],\n    [\n        \"ufdl\",\n        \"application/vnd.ufdl\"\n    ],\n    [\n        \"ulx\",\n        \"application/x-glulx\"\n    ],\n    [\n        \"umj\",\n        \"application/vnd.umajin\"\n    ],\n    [\n        \"unityweb\",\n        \"application/vnd.unity\"\n    ],\n    [\n        \"uoml\",\n        \"application/vnd.uoml+xml\"\n    ],\n    [\n        \"uri\",\n        \"text/uri-list\"\n    ],\n    [\n        \"uris\",\n        \"text/uri-list\"\n    ],\n    [\n        \"urls\",\n        \"text/uri-list\"\n    ],\n    [\n        \"usdz\",\n        \"model/vnd.usdz+zip\"\n    ],\n    [\n        \"ustar\",\n        \"application/x-ustar\"\n    ],\n    [\n        \"utz\",\n        \"application/vnd.uiq.theme\"\n    ],\n    [\n        \"uu\",\n        \"text/x-uuencode\"\n    ],\n    [\n        \"uva\",\n        \"audio/vnd.dece.audio\"\n    ],\n    [\n        \"uvd\",\n        \"application/vnd.dece.data\"\n    ],\n    [\n        \"uvf\",\n        \"application/vnd.dece.data\"\n    ],\n    [\n        \"uvg\",\n        \"image/vnd.dece.graphic\"\n    ],\n    [\n        \"uvh\",\n        \"video/vnd.dece.hd\"\n    ],\n    [\n        \"uvi\",\n        \"image/vnd.dece.graphic\"\n    ],\n    [\n        \"uvm\",\n        \"video/vnd.dece.mobile\"\n    ],\n    [\n        \"uvp\",\n        \"video/vnd.dece.pd\"\n    ],\n    [\n        \"uvs\",\n        \"video/vnd.dece.sd\"\n    ],\n    [\n        \"uvt\",\n        \"application/vnd.dece.ttml+xml\"\n    ],\n    [\n        \"uvu\",\n        \"video/vnd.uvvu.mp4\"\n    ],\n    [\n        \"uvv\",\n        \"video/vnd.dece.video\"\n    ],\n    [\n        \"uvva\",\n        \"audio/vnd.dece.audio\"\n    ],\n    [\n        \"uvvd\",\n        \"application/vnd.dece.data\"\n    ],\n    [\n        \"uvvf\",\n        \"application/vnd.dece.data\"\n    ],\n    [\n        \"uvvg\",\n        \"image/vnd.dece.graphic\"\n    ],\n    [\n        \"uvvh\",\n        \"video/vnd.dece.hd\"\n    ],\n    [\n        \"uvvi\",\n        \"image/vnd.dece.graphic\"\n    ],\n    [\n        \"uvvm\",\n        \"video/vnd.dece.mobile\"\n    ],\n    [\n        \"uvvp\",\n        \"video/vnd.dece.pd\"\n    ],\n    [\n        \"uvvs\",\n        \"video/vnd.dece.sd\"\n    ],\n    [\n        \"uvvt\",\n        \"application/vnd.dece.ttml+xml\"\n    ],\n    [\n        \"uvvu\",\n        \"video/vnd.uvvu.mp4\"\n    ],\n    [\n        \"uvvv\",\n        \"video/vnd.dece.video\"\n    ],\n    [\n        \"uvvx\",\n        \"application/vnd.dece.unspecified\"\n    ],\n    [\n        \"uvvz\",\n        \"application/vnd.dece.zip\"\n    ],\n    [\n        \"uvx\",\n        \"application/vnd.dece.unspecified\"\n    ],\n    [\n        \"uvz\",\n        \"application/vnd.dece.zip\"\n    ],\n    [\n        \"vbox\",\n        \"application/x-virtualbox-vbox\"\n    ],\n    [\n        \"vbox-extpack\",\n        \"application/x-virtualbox-vbox-extpack\"\n    ],\n    [\n        \"vcard\",\n        \"text/vcard\"\n    ],\n    [\n        \"vcd\",\n        \"application/x-cdlink\"\n    ],\n    [\n        \"vcf\",\n        \"text/x-vcard\"\n    ],\n    [\n        \"vcg\",\n        \"application/vnd.groove-vcard\"\n    ],\n    [\n        \"vcs\",\n        \"text/x-vcalendar\"\n    ],\n    [\n        \"vcx\",\n        \"application/vnd.vcx\"\n    ],\n    [\n        \"vdi\",\n        \"application/x-virtualbox-vdi\"\n    ],\n    [\n        \"vds\",\n        \"model/vnd.sap.vds\"\n    ],\n    [\n        \"vhd\",\n        \"application/x-virtualbox-vhd\"\n    ],\n    [\n        \"vis\",\n        \"application/vnd.visionary\"\n    ],\n    [\n        \"viv\",\n        \"video/vnd.vivo\"\n    ],\n    [\n        \"vlc\",\n        \"application/videolan\"\n    ],\n    [\n        \"vmdk\",\n        \"application/x-virtualbox-vmdk\"\n    ],\n    [\n        \"vob\",\n        \"video/x-ms-vob\"\n    ],\n    [\n        \"vor\",\n        \"application/vnd.stardivision.writer\"\n    ],\n    [\n        \"vox\",\n        \"application/x-authorware-bin\"\n    ],\n    [\n        \"vrml\",\n        \"model/vrml\"\n    ],\n    [\n        \"vsd\",\n        \"application/vnd.visio\"\n    ],\n    [\n        \"vsf\",\n        \"application/vnd.vsf\"\n    ],\n    [\n        \"vss\",\n        \"application/vnd.visio\"\n    ],\n    [\n        \"vst\",\n        \"application/vnd.visio\"\n    ],\n    [\n        \"vsw\",\n        \"application/vnd.visio\"\n    ],\n    [\n        \"vtf\",\n        \"image/vnd.valve.source.texture\"\n    ],\n    [\n        \"vtt\",\n        \"text/vtt\"\n    ],\n    [\n        \"vtu\",\n        \"model/vnd.vtu\"\n    ],\n    [\n        \"vxml\",\n        \"application/voicexml+xml\"\n    ],\n    [\n        \"w3d\",\n        \"application/x-director\"\n    ],\n    [\n        \"wad\",\n        \"application/x-doom\"\n    ],\n    [\n        \"wadl\",\n        \"application/vnd.sun.wadl+xml\"\n    ],\n    [\n        \"war\",\n        \"application/java-archive\"\n    ],\n    [\n        \"wasm\",\n        \"application/wasm\"\n    ],\n    [\n        \"wav\",\n        \"audio/x-wav\"\n    ],\n    [\n        \"wax\",\n        \"audio/x-ms-wax\"\n    ],\n    [\n        \"wbmp\",\n        \"image/vnd.wap.wbmp\"\n    ],\n    [\n        \"wbs\",\n        \"application/vnd.criticaltools.wbs+xml\"\n    ],\n    [\n        \"wbxml\",\n        \"application/wbxml\"\n    ],\n    [\n        \"wcm\",\n        \"application/vnd.ms-works\"\n    ],\n    [\n        \"wdb\",\n        \"application/vnd.ms-works\"\n    ],\n    [\n        \"wdp\",\n        \"image/vnd.ms-photo\"\n    ],\n    [\n        \"weba\",\n        \"audio/webm\"\n    ],\n    [\n        \"webapp\",\n        \"application/x-web-app-manifest+json\"\n    ],\n    [\n        \"webm\",\n        \"video/webm\"\n    ],\n    [\n        \"webmanifest\",\n        \"application/manifest+json\"\n    ],\n    [\n        \"webp\",\n        \"image/webp\"\n    ],\n    [\n        \"wg\",\n        \"application/vnd.pmi.widget\"\n    ],\n    [\n        \"wgt\",\n        \"application/widget\"\n    ],\n    [\n        \"wks\",\n        \"application/vnd.ms-works\"\n    ],\n    [\n        \"wm\",\n        \"video/x-ms-wm\"\n    ],\n    [\n        \"wma\",\n        \"audio/x-ms-wma\"\n    ],\n    [\n        \"wmd\",\n        \"application/x-ms-wmd\"\n    ],\n    [\n        \"wmf\",\n        \"image/wmf\"\n    ],\n    [\n        \"wml\",\n        \"text/vnd.wap.wml\"\n    ],\n    [\n        \"wmlc\",\n        \"application/wmlc\"\n    ],\n    [\n        \"wmls\",\n        \"text/vnd.wap.wmlscript\"\n    ],\n    [\n        \"wmlsc\",\n        \"application/vnd.wap.wmlscriptc\"\n    ],\n    [\n        \"wmv\",\n        \"video/x-ms-wmv\"\n    ],\n    [\n        \"wmx\",\n        \"video/x-ms-wmx\"\n    ],\n    [\n        \"wmz\",\n        \"application/x-msmetafile\"\n    ],\n    [\n        \"woff\",\n        \"font/woff\"\n    ],\n    [\n        \"woff2\",\n        \"font/woff2\"\n    ],\n    [\n        \"word\",\n        \"application/msword\"\n    ],\n    [\n        \"wpd\",\n        \"application/vnd.wordperfect\"\n    ],\n    [\n        \"wpl\",\n        \"application/vnd.ms-wpl\"\n    ],\n    [\n        \"wps\",\n        \"application/vnd.ms-works\"\n    ],\n    [\n        \"wqd\",\n        \"application/vnd.wqd\"\n    ],\n    [\n        \"wri\",\n        \"application/x-mswrite\"\n    ],\n    [\n        \"wrl\",\n        \"model/vrml\"\n    ],\n    [\n        \"wsc\",\n        \"message/vnd.wfa.wsc\"\n    ],\n    [\n        \"wsdl\",\n        \"application/wsdl+xml\"\n    ],\n    [\n        \"wspolicy\",\n        \"application/wspolicy+xml\"\n    ],\n    [\n        \"wtb\",\n        \"application/vnd.webturbo\"\n    ],\n    [\n        \"wvx\",\n        \"video/x-ms-wvx\"\n    ],\n    [\n        \"x3d\",\n        \"model/x3d+xml\"\n    ],\n    [\n        \"x3db\",\n        \"model/x3d+fastinfoset\"\n    ],\n    [\n        \"x3dbz\",\n        \"model/x3d+binary\"\n    ],\n    [\n        \"x3dv\",\n        \"model/x3d-vrml\"\n    ],\n    [\n        \"x3dvz\",\n        \"model/x3d+vrml\"\n    ],\n    [\n        \"x3dz\",\n        \"model/x3d+xml\"\n    ],\n    [\n        \"x32\",\n        \"application/x-authorware-bin\"\n    ],\n    [\n        \"x_b\",\n        \"model/vnd.parasolid.transmit.binary\"\n    ],\n    [\n        \"x_t\",\n        \"model/vnd.parasolid.transmit.text\"\n    ],\n    [\n        \"xaml\",\n        \"application/xaml+xml\"\n    ],\n    [\n        \"xap\",\n        \"application/x-silverlight-app\"\n    ],\n    [\n        \"xar\",\n        \"application/vnd.xara\"\n    ],\n    [\n        \"xav\",\n        \"application/xcap-att+xml\"\n    ],\n    [\n        \"xbap\",\n        \"application/x-ms-xbap\"\n    ],\n    [\n        \"xbd\",\n        \"application/vnd.fujixerox.docuworks.binder\"\n    ],\n    [\n        \"xbm\",\n        \"image/x-xbitmap\"\n    ],\n    [\n        \"xca\",\n        \"application/xcap-caps+xml\"\n    ],\n    [\n        \"xcs\",\n        \"application/calendar+xml\"\n    ],\n    [\n        \"xdf\",\n        \"application/xcap-diff+xml\"\n    ],\n    [\n        \"xdm\",\n        \"application/vnd.syncml.dm+xml\"\n    ],\n    [\n        \"xdp\",\n        \"application/vnd.adobe.xdp+xml\"\n    ],\n    [\n        \"xdssc\",\n        \"application/dssc+xml\"\n    ],\n    [\n        \"xdw\",\n        \"application/vnd.fujixerox.docuworks\"\n    ],\n    [\n        \"xel\",\n        \"application/xcap-el+xml\"\n    ],\n    [\n        \"xenc\",\n        \"application/xenc+xml\"\n    ],\n    [\n        \"xer\",\n        \"application/patch-ops-error+xml\"\n    ],\n    [\n        \"xfdf\",\n        \"application/vnd.adobe.xfdf\"\n    ],\n    [\n        \"xfdl\",\n        \"application/vnd.xfdl\"\n    ],\n    [\n        \"xht\",\n        \"application/xhtml+xml\"\n    ],\n    [\n        \"xhtml\",\n        \"application/xhtml+xml\"\n    ],\n    [\n        \"xhvml\",\n        \"application/xv+xml\"\n    ],\n    [\n        \"xif\",\n        \"image/vnd.xiff\"\n    ],\n    [\n        \"xl\",\n        \"application/excel\"\n    ],\n    [\n        \"xla\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xlam\",\n        \"application/vnd.ms-excel.addin.macroEnabled.12\"\n    ],\n    [\n        \"xlc\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xlf\",\n        \"application/xliff+xml\"\n    ],\n    [\n        \"xlm\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xls\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xlsb\",\n        \"application/vnd.ms-excel.sheet.binary.macroEnabled.12\"\n    ],\n    [\n        \"xlsm\",\n        \"application/vnd.ms-excel.sheet.macroEnabled.12\"\n    ],\n    [\n        \"xlsx\",\n        \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n    ],\n    [\n        \"xlt\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xltm\",\n        \"application/vnd.ms-excel.template.macroEnabled.12\"\n    ],\n    [\n        \"xltx\",\n        \"application/vnd.openxmlformats-officedocument.spreadsheetml.template\"\n    ],\n    [\n        \"xlw\",\n        \"application/vnd.ms-excel\"\n    ],\n    [\n        \"xm\",\n        \"audio/xm\"\n    ],\n    [\n        \"xml\",\n        \"application/xml\"\n    ],\n    [\n        \"xns\",\n        \"application/xcap-ns+xml\"\n    ],\n    [\n        \"xo\",\n        \"application/vnd.olpc-sugar\"\n    ],\n    [\n        \"xop\",\n        \"application/xop+xml\"\n    ],\n    [\n        \"xpi\",\n        \"application/x-xpinstall\"\n    ],\n    [\n        \"xpl\",\n        \"application/xproc+xml\"\n    ],\n    [\n        \"xpm\",\n        \"image/x-xpixmap\"\n    ],\n    [\n        \"xpr\",\n        \"application/vnd.is-xpr\"\n    ],\n    [\n        \"xps\",\n        \"application/vnd.ms-xpsdocument\"\n    ],\n    [\n        \"xpw\",\n        \"application/vnd.intercon.formnet\"\n    ],\n    [\n        \"xpx\",\n        \"application/vnd.intercon.formnet\"\n    ],\n    [\n        \"xsd\",\n        \"application/xml\"\n    ],\n    [\n        \"xsl\",\n        \"application/xml\"\n    ],\n    [\n        \"xslt\",\n        \"application/xslt+xml\"\n    ],\n    [\n        \"xsm\",\n        \"application/vnd.syncml+xml\"\n    ],\n    [\n        \"xspf\",\n        \"application/xspf+xml\"\n    ],\n    [\n        \"xul\",\n        \"application/vnd.mozilla.xul+xml\"\n    ],\n    [\n        \"xvm\",\n        \"application/xv+xml\"\n    ],\n    [\n        \"xvml\",\n        \"application/xv+xml\"\n    ],\n    [\n        \"xwd\",\n        \"image/x-xwindowdump\"\n    ],\n    [\n        \"xyz\",\n        \"chemical/x-xyz\"\n    ],\n    [\n        \"xz\",\n        \"application/x-xz\"\n    ],\n    [\n        \"yaml\",\n        \"text/yaml\"\n    ],\n    [\n        \"yang\",\n        \"application/yang\"\n    ],\n    [\n        \"yin\",\n        \"application/yin+xml\"\n    ],\n    [\n        \"yml\",\n        \"text/yaml\"\n    ],\n    [\n        \"ymp\",\n        \"text/x-suse-ymp\"\n    ],\n    [\n        \"z\",\n        \"application/x-compress\"\n    ],\n    [\n        \"z1\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z2\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z3\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z4\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z5\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z6\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z7\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"z8\",\n        \"application/x-zmachine\"\n    ],\n    [\n        \"zaz\",\n        \"application/vnd.zzazz.deck+xml\"\n    ],\n    [\n        \"zip\",\n        \"application/zip\"\n    ],\n    [\n        \"zir\",\n        \"application/vnd.zul\"\n    ],\n    [\n        \"zirz\",\n        \"application/vnd.zul\"\n    ],\n    [\n        \"zmm\",\n        \"application/vnd.handheld-entertainment+xml\"\n    ],\n    [\n        \"zsh\",\n        \"text/x-scriptzsh\"\n    ]\n]);\nfunction toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === \"string\" ? path : typeof webkitRelativePath === \"string\" && webkitRelativePath.length > 0 ? webkitRelativePath : `./${file.name}`;\n    if (typeof f.path !== \"string\") {\n        setObjProp(f, \"path\", p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, \"handle\", {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, \"relativePath\", p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf(\".\") !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split(\".\").pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, \"type\", {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n} //# sourceMappingURL=file.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* reexport safe */ _file_selector__WEBPACK_IMPORTED_MODULE_0__.fromEvent)\n/* harmony export */ });\n/* harmony import */ var _file_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\");\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QyxDQUM1QyxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZXN1bWF0Y2gtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9pbmRleC5qcz9mYTY0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGZyb21FdmVudCB9IGZyb20gJy4vZmlsZS1zZWxlY3Rvcic7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOlsiZnJvbUV2ZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/index.js\n");

/***/ })

};
;