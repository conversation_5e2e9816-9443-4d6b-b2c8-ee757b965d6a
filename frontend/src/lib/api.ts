import axios, { AxiosResponse } from 'axios';
import {
  JobDescription,
  JobMatch,
  ResumeMatchResults,
  ResumeData,
  UploadResponse,
  MatchingParams,
  ApiError,
} from '@/types/api';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add withCredentials for CORS if needed
  withCredentials: false,
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);

    let errorMessage = 'An unexpected error occurred';

    // Handle different types of errors
    if (error.code === 'ERR_NETWORK' || error.code === 'ECONNREFUSED') {
      errorMessage = 'Cannot connect to the ResuMatch API. Please ensure the backend server is running on http://localhost:8000';
    } else if (error.code === 'ERR_CONNECTION_REFUSED') {
      errorMessage = 'Connection refused. The ResuMatch backend server may not be running.';
    } else if (error.response?.status === 404) {
      errorMessage = 'API endpoint not found. Please check the backend configuration.';
    } else if (error.response?.status >= 500) {
      errorMessage = 'Server error. Please try again later.';
    } else if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Transform error to our ApiError format
    const apiError: ApiError = {
      detail: errorMessage,
      status_code: error.response?.status,
    };

    return Promise.reject(apiError);
  }
);

// API Functions

/**
 * Upload a resume PDF file
 */
export const uploadResume = async (
  file: File,
  name?: string,
  userId?: string,
  onProgress?: (progress: number) => void
): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  if (name) formData.append('name', name);
  if (userId) formData.append('user_id', userId);

  const response: AxiosResponse<UploadResponse> = await api.post(
    '/api/documents/upload',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    }
  );

  return response.data;
};

/**
 * Get job matches for a resume
 */
export const getJobMatches = async (params: MatchingParams): Promise<ResumeMatchResults> => {
  const queryParams = new URLSearchParams();
  queryParams.append('resume_id', params.resume_id);
  if (params.threshold !== undefined) queryParams.append('threshold', params.threshold.toString());
  if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());
  if (params.use_cache !== undefined) queryParams.append('use_cache', params.use_cache.toString());
  if (params.job_id) queryParams.append('job_id', params.job_id);

  const response: AxiosResponse<ResumeMatchResults> = await api.get(
    `/api/documents/match?${queryParams.toString()}`
  );

  return response.data;
};

/**
 * Get all available jobs
 */
export const getJobs = async (): Promise<JobDescription[]> => {
  const response: AxiosResponse<JobDescription[]> = await api.get('/api/jobs');
  return response.data;
};

/**
 * Get a specific job by ID
 */
export const getJob = async (jobId: string): Promise<JobDescription> => {
  const response: AxiosResponse<JobDescription> = await api.get(`/api/jobs/${jobId}`);
  return response.data;
};

/**
 * Create a new job posting
 */
export const createJob = async (job: Omit<JobDescription, 'job_id' | 'created_at'>): Promise<{ job_id: string }> => {
  const response: AxiosResponse<{ job_id: string }> = await api.post('/api/documents/job', job);
  return response.data;
};

/**
 * Get detailed resume information
 */
export const getResume = async (resumeId: string): Promise<ResumeData> => {
  const response: AxiosResponse<ResumeData> = await api.get(`/api/resumes/${resumeId}`);
  return response.data;
};

/**
 * Health check endpoint
 */
export const healthCheck = async (): Promise<{ message: string; version: string }> => {
  const response: AxiosResponse<{ message: string; version: string }> = await api.get('/');
  return response.data;
};

export default api;
