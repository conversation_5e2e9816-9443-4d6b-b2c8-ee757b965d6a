import { z } from 'zod';

// Client-side file validation function (avoids SSR issues with File constructor)
const isValidFile = (value: any): value is File => {
  // Check if we're in a browser environment and File constructor exists
  if (typeof window === 'undefined' || typeof File === 'undefined') {
    return false;
  }
  return value instanceof File;
};

// File validation schemas - using custom validation to avoid SSR issues
export const fileValidationSchema = z.object({
  file: z
    .any()
    .refine((file) => isValidFile(file), {
      message: 'Please select a valid file',
    })
    .refine((file) => isValidFile(file) && file.type === 'application/pdf', {
      message: 'Only PDF files are allowed',
    })
    .refine((file) => isValidFile(file) && file.size <= 10 * 1024 * 1024, {
      message: 'File size must be less than 10MB',
    }),
});

// Job form validation schema
export const jobFormSchema = z.object({
  title: z
    .string()
    .min(1, 'Job title is required')
    .min(3, 'Job title must be at least 3 characters')
    .max(100, 'Job title must be less than 100 characters'),
  company: z
    .string()
    .min(1, 'Company name is required')
    .min(2, 'Company name must be at least 2 characters')
    .max(100, 'Company name must be less than 100 characters'),
  description: z
    .string()
    .min(1, 'Job description is required')
    .min(50, 'Job description must be at least 50 characters')
    .max(5000, 'Job description must be less than 5000 characters'),
  requirements: z
    .string()
    .max(3000, 'Requirements must be less than 3000 characters')
    .optional(),
  responsibilities: z
    .string()
    .max(3000, 'Responsibilities must be less than 3000 characters')
    .optional(),
  location: z
    .string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
});

// Matching parameters validation schema
export const matchingParamsSchema = z.object({
  threshold: z
    .number()
    .min(0, 'Threshold must be between 0 and 1')
    .max(1, 'Threshold must be between 0 and 1')
    .optional(),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit must be at most 100')
    .optional(),
  use_cache: z.boolean().optional(),
});

// Contact information validation
export const contactValidationSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .optional()
    .or(z.literal('')),
  phone: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format')
    .optional()
    .or(z.literal('')),
});

// Validation helper functions
export const validateFile = (file: File): { isValid: boolean; error?: string } => {
  // Client-side only validation
  if (typeof window === 'undefined') {
    return { isValid: false, error: 'File validation only available on client-side' };
  }

  // Manual validation for better error messages
  if (!file) {
    return { isValid: false, error: 'Please select a file' };
  }

  if (!isValidFile(file)) {
    return { isValid: false, error: 'Please select a valid file' };
  }

  if (file.type !== 'application/pdf') {
    return { isValid: false, error: 'Only PDF files are allowed' };
  }

  if (file.size > 10 * 1024 * 1024) {
    return { isValid: false, error: 'File size must be less than 10MB' };
  }

  return { isValid: true };
};

export const validateJobForm = (data: any): { isValid: boolean; errors?: Record<string, string> } => {
  try {
    jobFormSchema.parse(data);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach((err) => {
        if (err.path.length > 0) {
          errors[err.path[0] as string] = err.message;
        }
      });
      return { isValid: false, errors };
    }
    return { isValid: false, errors: { general: 'Validation failed' } };
  }
};

export const validateMatchingParams = (data: any): { isValid: boolean; error?: string } => {
  try {
    matchingParamsSchema.parse(data);
    return { isValid: true };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { isValid: false, error: error.errors[0].message };
    }
    return { isValid: false, error: 'Invalid parameters' };
  }
};

// Type exports for form data
export type JobFormData = z.infer<typeof jobFormSchema>;
export type MatchingParams = z.infer<typeof matchingParamsSchema>;
export type ContactData = z.infer<typeof contactValidationSchema>;
