import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import re
import os
import json
from typing import Dict, List, Tuple
from datetime import datetime

class LegacySiameseNetwork(nn.Module):
    """
    Legacy Siamese neural network for matching resume embeddings with job description embeddings.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, input_dim, hidden_dim=256, output_dim=128):
        """
        Initialize the Legacy Siamese Network with architecture matching the saved weights

        Args:
            input_dim (int): Dimension of input embeddings
            hidden_dim (int): Dimension of hidden layer
            output_dim (int): Dimension of output embeddings (shared space)
        """
        super(LegacySiameseNetwork, self).__init__()

        # Define network architecture to exactly match the saved model
        # Based on the error messages, the saved model has this structure:
        # encoder.0.weight - Linear layer (384 -> 256)
        # encoder.2.weight, bias, running_mean, running_var, num_batches_tracked - BatchNorm
        # encoder.4.weight, bias - Linear layer (256 -> 128)
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),  # encoder.0
            nn.ReLU(),                         # encoder.1
            nn.BatchNorm1d(hidden_dim),        # encoder.2
            nn.Dropout(0.2),                   # encoder.3
            nn.Linear(hidden_dim, output_dim), # encoder.4
        )

    def forward(self, x):
        """Forward pass through the network"""
        # Handle both single samples and batches
        if x.dim() == 1:
            # Add batch dimension for single samples
            x = x.unsqueeze(0)

        # Handle batch normalization for single samples
        if x.size(0) == 1:
            # Use eval mode for batch norm with single sample
            training = self.training
            self.eval()
            with torch.no_grad():
                output = self.encoder(x)
            self.train(training)
            return output
        else:
            return self.encoder(x)

    def forward_pair(self, x1, x2):
        """Forward pass for a pair of inputs"""
        output1 = self.forward(x1)
        output2 = self.forward(x2)
        return output1, output2


class LegacyResumeMatcher:
    """
    Class to match resumes with job descriptions using a Legacy Siamese network.
    This class is designed to be compatible with older saved model weights.
    """

    def __init__(self, embedding_dim, model_path=None):
        """
        Initialize the LegacyResumeMatcher

        Args:
            embedding_dim (int): Dimension of input embeddings
            model_path (str, optional): Path to pre-trained model weights
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # For the legacy model, we need to use the exact dimensions from the saved model
        # The saved model expects input_dim=384, but our current embeddings are 300
        # We'll pad the embeddings to match
        self.input_dim = 384  # Fixed to match saved model
        self.embedding_dim = embedding_dim  # Actual embedding dimension

        self.model = LegacySiameseNetwork(self.input_dim, hidden_dim=256, output_dim=128).to(self.device)

        if model_path and os.path.exists(model_path):
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()

    def match(self, resume_embedding, job_embeddings_list, similarity_threshold=0.7):
        """
        Match a resume with multiple job descriptions

        Args:
            resume_embedding (np.ndarray): Embedding of a resume
            job_embeddings_list (List[np.ndarray]): List of job description embeddings
            similarity_threshold (float): Threshold for considering a match

        Returns:
            List[Tuple[int, float]]: List of (job_index, similarity_score) sorted by similarity
        """
        self.model.eval()

        # Pad the resume embedding to match the expected input dimension
        padded_resume_embedding = self._pad_embedding(resume_embedding)

        # Convert resume embedding to tensor
        resume_tensor = torch.tensor(padded_resume_embedding, dtype=torch.float32).to(self.device)

        matches = []

        with torch.no_grad():
            # Encode resume
            resume_encoded = self.model(resume_tensor)

            # Process each job embedding
            for i, job_embedding in enumerate(job_embeddings_list):
                # Pad the job embedding
                padded_job_embedding = self._pad_embedding(job_embedding)

                # Convert job embedding to tensor
                job_tensor = torch.tensor(padded_job_embedding, dtype=torch.float32).to(self.device)

                # Encode job
                job_encoded = self.model(job_tensor)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(resume_encoded, job_encoded).item()

                if similarity >= similarity_threshold:
                    matches.append((i, similarity))

        # Sort matches by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)

        return matches

    def _pad_embedding(self, embedding):
        """
        Pad embedding to match the expected input dimension of the legacy model

        Args:
            embedding (np.ndarray): Original embedding

        Returns:
            np.ndarray: Padded embedding
        """
        # Handle case where embedding is a 2D array
        if isinstance(embedding, np.ndarray) and embedding.ndim > 1:
            # If it's a batch of embeddings, process the first one
            embedding = embedding[0]

        if len(embedding) >= self.input_dim:
            # If embedding is already large enough, truncate it
            return embedding[:self.input_dim]
        else:
            # Pad with zeros
            padded = np.zeros(self.input_dim)
            padded[:len(embedding)] = embedding
            return padded

    def _cosine_similarity(self, x1, x2):
        """Calculate cosine similarity between two vectors"""
        return torch.nn.functional.cosine_similarity(x1, x2, dim=1)

    def get_similarity(self, resume_embedding, job_embedding, resume_text=None, job_text=None):
        """
        Calculate enhanced similarity between a resume and job embedding with skill-aware scoring

        Args:
            resume_embedding (np.ndarray): Resume embedding vector
            job_embedding (np.ndarray): Job embedding vector
            resume_text (str, optional): Resume text for skill analysis
            job_text (str, optional): Job text for skill analysis

        Returns:
            float: Similarity score between 0 and 1
        """
        self.model.eval()

        # Pad embeddings to match expected input dimension
        padded_resume_embedding = self._pad_embedding(resume_embedding)
        padded_job_embedding = self._pad_embedding(job_embedding)

        # Convert embeddings to tensors
        resume_tensor = torch.tensor(padded_resume_embedding, dtype=torch.float32).to(self.device)
        job_tensor = torch.tensor(padded_job_embedding, dtype=torch.float32).to(self.device)

        with torch.no_grad():
            try:
                # Calculate base similarity using embeddings
                base_similarity = self._calculate_base_similarity(resume_tensor, job_tensor, resume_embedding, job_embedding)

                # If we have text data, enhance with skill-aware scoring
                if resume_text and job_text:
                    enhanced_similarity = self._calculate_enhanced_similarity(base_similarity, resume_text, job_text)
                    return enhanced_similarity
                else:
                    # Apply improved scaling for embedding-only similarity
                    return self._apply_improved_scaling(base_similarity)

            except Exception as e:
                # Fallback to direct cosine similarity if anything fails
                return self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

    def _calculate_base_similarity(self, resume_tensor, job_tensor, resume_embedding, job_embedding):
        """Calculate base similarity using both Siamese network and direct cosine similarity"""
        try:
            # Encode both embeddings using Siamese network
            resume_encoded = self.model(resume_tensor)
            job_encoded = self.model(job_tensor)

            # Calculate cosine similarity from Siamese network
            siamese_similarity = self._cosine_similarity(resume_encoded, job_encoded).item()
        except:
            siamese_similarity = 0.5  # Neutral fallback

        # Calculate direct cosine similarity between original embeddings
        direct_similarity = self._calculate_direct_cosine_similarity(resume_embedding, job_embedding)

        # Combine similarities with adjusted weights
        # Reduce Siamese network weight since it's not well-trained for our specific use case
        combined_similarity = 0.2 * siamese_similarity + 0.8 * direct_similarity

        return combined_similarity

    def _calculate_direct_cosine_similarity(self, embedding1, embedding2):
        """Calculate direct cosine similarity between two embeddings"""
        # Ensure embeddings are numpy arrays
        if isinstance(embedding1, torch.Tensor):
            embedding1 = embedding1.cpu().numpy()
        if isinstance(embedding2, torch.Tensor):
            embedding2 = embedding2.cpu().numpy()

        # Flatten if needed
        embedding1 = embedding1.flatten()
        embedding2 = embedding2.flatten()

        # Calculate cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)

        # Ensure similarity is between 0 and 1
        similarity = max(0.0, min(1.0, (similarity + 1) / 2))  # Convert from [-1,1] to [0,1]

        return similarity

    def _scale_similarity(self, similarity):
        """Scale similarity to provide more realistic and varied scores"""
        # Apply a sigmoid-like transformation to spread out scores
        import math

        # Convert to a range that gives more realistic job matching scores
        # Most job matches should be in the 0.3-0.9 range, not 0.95-0.99

        # Apply a power transformation to reduce very high similarities
        scaled = math.pow(similarity, 1.5)

        # Apply additional scaling to get more realistic ranges
        # Map [0,1] to approximately [0.2, 0.95] with most values in [0.3, 0.8]
        final_score = 0.2 + (scaled * 0.75)

        return min(0.95, max(0.15, final_score))

    def _calculate_enhanced_similarity(self, base_similarity, resume_text, job_text):
        """Calculate enhanced similarity using job-requirement-based matching"""

        # Extract skills from both texts
        resume_skills = self._extract_all_skills(resume_text.lower())
        job_requirements = self._analyze_job_requirements(job_text.lower())

        # Calculate requirement-based skill matching score
        requirement_score = self._calculate_requirement_based_matching(resume_skills, job_requirements)

        # Check for non-tech job early exit
        if self._is_non_tech_job(job_text.lower()):
            # For clearly non-tech jobs, apply severe penalty
            return min(0.35, base_similarity * 0.3)

        # Check for QA job special handling
        if self._is_qa_job(job_text.lower()):
            # QA jobs should be evaluated differently - cap the score for mobile developers
            resume_has_mobile_skills = any(skill in resume_text.lower() for skill in ['flutter', 'android', 'ios', 'mobile'])
            resume_has_qa_skills = any(skill in resume_text.lower() for skill in ['testing', 'qa', 'quality assurance'])

            if resume_has_mobile_skills and not resume_has_qa_skills:
                # Mobile developer applying for QA - moderate relevance
                return min(0.60, base_similarity * 0.8)

        # Calculate domain relevance score
        domain_score = self._calculate_domain_relevance(resume_text.lower(), job_text.lower())

        # Calculate experience level alignment
        experience_score = self._calculate_experience_alignment(resume_text.lower(), job_text.lower())

        # Balanced weighting system for better overall matching
        # Requirement matching: 50%, Base similarity: 35%, Domain relevance: 10%, Experience: 5%
        enhanced_similarity = (
            0.35 * base_similarity +
            0.50 * requirement_score +
            0.10 * domain_score +
            0.05 * experience_score
        )

        # Apply requirement-based scaling
        return self._apply_requirement_based_scaling(enhanced_similarity, requirement_score, domain_score)

    def _extract_all_skills(self, text):
        """Extract all technical skills from text with equal weighting"""
        # Comprehensive skill list without category bias
        all_skills = [
            # Programming languages
            'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust', 'swift', 'kotlin', 'dart',
            'php', 'ruby', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash', 'powershell',

            # Mobile development
            'flutter', 'android', 'ios', 'react native', 'xamarin', 'ionic', 'cordova', 'riverpod', 'bloc',
            'provider', 'getx', 'redux', 'android studio', 'xcode', 'play store', 'app store',

            # Web technologies
            'react', 'angular', 'vue', 'html', 'css', 'bootstrap', 'tailwind', 'fastapi', 'django',
            'flask', 'express', 'spring', 'asp.net', 'node.js', 'next.js', 'nuxt.js', 'svelte',

            # Databases
            'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'firestore', 'oracle', 'sql server',
            'cassandra', 'elasticsearch', 'dynamodb', 'neo4j',

            # Cloud and DevOps
            'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'terraform', 'ansible',
            'gitlab ci', 'github actions', 'circleci', 'travis ci',

            # APIs and protocols
            'rest', 'graphql', 'websocket', 'json', 'xml', 'grpc', 'soap', 'oauth', 'jwt',

            # Data science and ML
            'tensorflow', 'pytorch', 'pandas', 'numpy', 'scikit-learn', 'matplotlib', 'seaborn',
            'jupyter', 'spark', 'hadoop', 'kafka', 'airflow',

            # Tools and frameworks
            'git', 'github', 'gitlab', 'bitbucket', 'linux', 'windows', 'macos', 'sql', 'nosql',
            'microservices', 'agile', 'scrum', 'kanban', 'jira', 'confluence'
        ]

        found_skills = []
        for skill in all_skills:
            if re.search(r'\b' + re.escape(skill) + r'\b', text, re.IGNORECASE):
                found_skills.append(skill.lower())

        return list(set(found_skills))  # Remove duplicates

    def _analyze_job_requirements(self, job_text):
        """Analyze job text to identify required vs preferred skills with dynamic weighting"""

        # Split job text into sections
        sections = self._split_job_sections(job_text)

        # Extract skills from different sections with different weights
        required_skills = []
        preferred_skills = []

        # Look for required skills in requirements section
        requirements_text = sections.get('requirements', '')
        if requirements_text:
            required_skills.extend(self._extract_skills_from_section(requirements_text, is_required=True))

        # Look for preferred skills in nice-to-have sections
        nice_to_have_text = sections.get('nice_to_have', '')
        if nice_to_have_text:
            preferred_skills.extend(self._extract_skills_from_section(nice_to_have_text, is_required=False))

        # If no clear sections, analyze the entire text for skill frequency
        if not required_skills and not preferred_skills:
            all_skills = self._extract_all_skills(job_text)
            skill_frequencies = self._calculate_skill_frequencies(job_text, all_skills)

            # Skills mentioned multiple times or with strong indicators are considered required
            for skill, frequency in skill_frequencies.items():
                if frequency > 1 or self._has_strong_requirement_indicators(job_text, skill):
                    required_skills.append(skill)
                else:
                    preferred_skills.append(skill)

        # If still no skills found, extract from job title and description
        if not required_skills and not preferred_skills:
            all_skills = self._extract_all_skills(job_text)
            # Consider all found skills as required if no clear distinction
            required_skills = all_skills[:5]  # Take top 5 skills as required
            preferred_skills = all_skills[5:]  # Rest as preferred

        return {
            'required': list(set(required_skills)),
            'preferred': list(set(preferred_skills)),
            'all_skills': list(set(required_skills + preferred_skills))
        }

    def _split_job_sections(self, job_text):
        """Split job description into different sections"""
        sections = {}

        # Common section headers
        requirement_patterns = [
            r'requirements?:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\n\s*qualifications|\Z)',
            r'qualifications?:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\Z)',
            r'must have:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\Z)'
        ]

        nice_to_have_patterns = [
            r'nice to have:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\Z)',
            r'preferred:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\Z)',
            r'bonus:?\s*\n(.*?)(?=\n\s*[a-z\s]*:|\n\s*responsibilities|\Z)'
        ]

        # Extract requirements section
        for pattern in requirement_patterns:
            match = re.search(pattern, job_text, re.IGNORECASE | re.DOTALL)
            if match:
                sections['requirements'] = match.group(1).strip()
                break

        # Extract nice-to-have section
        for pattern in nice_to_have_patterns:
            match = re.search(pattern, job_text, re.IGNORECASE | re.DOTALL)
            if match:
                sections['nice_to_have'] = match.group(1).strip()
                break

        return sections

    def _extract_skills_from_section(self, section_text, is_required=True):
        """Extract skills from a specific section of job description"""
        return self._extract_all_skills(section_text)

    def _calculate_skill_frequencies(self, job_text, skills):
        """Calculate how frequently each skill is mentioned in the job text"""
        frequencies = {}
        for skill in skills:
            count = len(re.findall(r'\b' + re.escape(skill) + r'\b', job_text, re.IGNORECASE))
            if count > 0:
                frequencies[skill] = count
        return frequencies

    def _has_strong_requirement_indicators(self, job_text, skill):
        """Check if a skill has strong requirement indicators in the job text"""
        # Look for the skill near requirement keywords
        requirement_keywords = ['required', 'must have', 'essential', 'mandatory', 'critical', 'necessary']

        for keyword in requirement_keywords:
            # Look for the skill within 50 characters of requirement keywords
            pattern = f'({re.escape(keyword)}.{{0,50}}{re.escape(skill)}|{re.escape(skill)}.{{0,50}}{re.escape(keyword)})'
            if re.search(pattern, job_text, re.IGNORECASE):
                return True
        return False

    def _calculate_requirement_based_matching(self, resume_skills, job_requirements):
        """Calculate matching score based on job requirements with flexible matching"""

        required_skills = set(job_requirements['required'])
        preferred_skills = set(job_requirements['preferred'])
        resume_skills_set = set(resume_skills)

        if not required_skills and not preferred_skills:
            return 0.5  # Neutral score if no skills identified in job

        # Calculate exact matches
        required_exact_matches = len(resume_skills_set.intersection(required_skills))
        preferred_exact_matches = len(resume_skills_set.intersection(preferred_skills))

        # Calculate related skill matches (more flexible)
        required_related_matches = self._calculate_related_skill_matches(resume_skills, required_skills)
        preferred_related_matches = self._calculate_related_skill_matches(resume_skills, preferred_skills)

        total_required = len(required_skills)
        total_preferred = len(preferred_skills)

        # Calculate scores with both exact and related matches
        if total_required > 0:
            # Give more weight to exact matches, but also credit related matches
            required_score = (required_exact_matches * 1.0 + required_related_matches * 0.7) / total_required
        else:
            required_score = 1.0

        if total_preferred > 0:
            preferred_score = (preferred_exact_matches * 1.0 + preferred_related_matches * 0.7) / total_preferred
        else:
            preferred_score = 1.0

        # Weight required skills higher than preferred, but not as aggressively
        # Required skills: 70% weight, Preferred skills: 30% weight
        if total_required > 0:
            weighted_score = 0.7 * required_score + 0.3 * preferred_score
        else:
            # If no required skills identified, use preferred skills only
            weighted_score = preferred_score

        # Apply more moderate penalties for missing skills
        if total_required > 0:
            exact_required_percentage = required_exact_matches / total_required
            total_required_percentage = min(1.0, (required_exact_matches + required_related_matches) / total_required)

            if exact_required_percentage < 0.2 and total_required_percentage < 0.4:
                # Very poor match - moderate penalty
                weighted_score *= 0.6
            elif exact_required_percentage < 0.4 and total_required_percentage < 0.6:
                # Poor match - light penalty
                weighted_score *= 0.8
            elif total_required_percentage >= 0.8:
                # Good match - small bonus
                weighted_score *= 1.1

        # Apply bonus for having relevant additional skills
        all_job_skills = required_skills.union(preferred_skills)
        extra_relevant_skills = len(resume_skills_set.intersection(all_job_skills)) - required_exact_matches - preferred_exact_matches
        if extra_relevant_skills > 2:
            weighted_score *= 1.05  # Small bonus for additional relevant skills

        return min(1.0, max(0.1, weighted_score))  # Ensure minimum score of 0.1

    def _calculate_related_skill_matches(self, resume_skills, job_skills):
        """Calculate matches for related skills (e.g., Python matches Django, FastAPI)"""
        related_matches = 0
        resume_skills_set = set(resume_skills)

        # Define skill relationships
        skill_relationships = {
            'python': ['django', 'flask', 'fastapi', 'pandas', 'numpy'],
            'javascript': ['react', 'angular', 'vue', 'node.js', 'express'],
            'java': ['spring', 'hibernate', 'maven', 'gradle'],
            'flutter': ['dart', 'android', 'ios', 'mobile'],
            'react': ['javascript', 'typescript', 'jsx', 'redux'],
            'android': ['java', 'kotlin', 'flutter', 'mobile'],
            'ios': ['swift', 'objective-c', 'flutter', 'mobile'],
            'aws': ['cloud', 'docker', 'kubernetes', 'devops'],
            'docker': ['kubernetes', 'devops', 'containerization'],
            'sql': ['mysql', 'postgresql', 'database'],
            'nosql': ['mongodb', 'redis', 'database']
        }

        for job_skill in job_skills:
            if job_skill not in resume_skills_set:
                # Check if resume has related skills
                related_skills = skill_relationships.get(job_skill, [])
                if any(related_skill in resume_skills_set for related_skill in related_skills):
                    related_matches += 1

                # Also check reverse relationships
                for resume_skill in resume_skills_set:
                    if job_skill in skill_relationships.get(resume_skill, []):
                        related_matches += 1
                        break

        return related_matches

    def _calculate_domain_relevance(self, resume_text, job_text):
        """Calculate domain relevance score with enhanced non-tech job detection"""
        # Enhanced domain keywords with more specific non-tech indicators
        domain_keywords = {
            'software_dev': ['software', 'programming', 'coding', 'developer', 'engineer', 'technical'],
            'mobile_app': ['mobile', 'app', 'application', 'smartphone', 'tablet', 'flutter', 'android', 'ios'],
            'web_dev': ['web', 'website', 'frontend', 'backend', 'fullstack', 'full stack', 'react', 'angular'],
            'data_science': ['data science', 'machine learning', 'ai', 'analytics', 'big data', 'python', 'tensorflow'],
            'devops': ['devops', 'infrastructure', 'deployment', 'ci/cd', 'automation', 'docker', 'kubernetes'],
            'qa_testing': ['qa', 'quality assurance', 'testing', 'test', 'automation testing', 'manual testing'],
            'design': ['ui', 'ux', 'design', 'user interface', 'user experience', 'graphic design'],

            # Non-tech domains with stronger indicators
            'business_marketing': ['marketing', 'sales', 'business development', 'strategy', 'campaigns', 'advertising'],
            'finance_accounting': ['accounting', 'finance', 'financial', 'budget', 'audit', 'bookkeeping', 'tax'],
            'hr_management': ['human resources', 'hr', 'recruitment', 'hiring', 'employee', 'personnel'],
            'operations': ['operations', 'supply chain', 'logistics', 'procurement', 'inventory'],
            'mechanical_engineering': ['mechanical', 'electrical', 'civil', 'manufacturing', 'product design', 'cad']
        }

        resume_domains = set()
        job_domains = set()

        for domain, keywords in domain_keywords.items():
            for keyword in keywords:
                if re.search(r'\b' + re.escape(keyword) + r'\b', resume_text, re.IGNORECASE):
                    resume_domains.add(domain)
                if re.search(r'\b' + re.escape(keyword) + r'\b', job_text, re.IGNORECASE):
                    job_domains.add(domain)

        # Enhanced domain classification
        tech_domains = {'software_dev', 'mobile_app', 'web_dev', 'data_science', 'devops', 'qa_testing', 'design'}
        non_tech_domains = {'business_marketing', 'finance_accounting', 'hr_management', 'operations', 'mechanical_engineering'}

        resume_is_tech = bool(resume_domains.intersection(tech_domains))
        job_is_tech = bool(job_domains.intersection(tech_domains))
        job_is_non_tech = bool(job_domains.intersection(non_tech_domains))

        # Special handling for QA Engineer mobile testing bias
        job_is_qa = 'qa_testing' in job_domains
        resume_has_mobile = 'mobile_app' in resume_domains

        # Check if this is specifically a QA job (even if it mentions mobile testing)
        if job_is_qa:
            # QA jobs should be evaluated on testing skills, not mobile development
            if resume_has_mobile and not any(d in resume_domains for d in ['qa_testing']):
                # Mobile developer applying for QA job - moderate relevance only
                return 0.3  # Lower relevance for QA testing role

        if not job_domains:
            return 0.5  # Neutral if no clear domain

        overlap = len(resume_domains.intersection(job_domains))
        total_job_domains = len(job_domains)

        # Calculate base overlap score
        if total_job_domains == 0:
            return 0.5

        overlap_score = overlap / total_job_domains

        # Apply aggressive penalties for clear domain mismatches
        if resume_is_tech and job_is_non_tech:
            # Very strong penalty for tech resume vs non-tech job
            if overlap == 0:
                return 0.05  # Almost zero relevance
            else:
                return max(0.1, overlap_score * 0.2)  # 80% penalty

        # Moderate penalty for tech resume vs non-tech job with some overlap
        if resume_is_tech and job_is_non_tech and overlap > 0:
            return max(0.15, overlap_score * 0.3)  # 70% penalty

        # Penalty for non-tech resume vs tech job
        if not resume_is_tech and job_is_tech:
            return max(0.2, overlap_score * 0.4)  # 60% penalty

        return overlap_score

    def _is_non_tech_job(self, job_text):
        """Detect if a job is clearly non-technical and requires zero technical skills"""
        # Strong indicators of non-tech jobs
        non_tech_indicators = [
            'marketing manager', 'sales representative', 'human resources', 'hr manager',
            'accountant', 'financial analyst', 'mechanical engineer', 'operations manager',
            'supply chain', 'business development', 'account manager', 'project coordinator',
            'administrative', 'customer service', 'retail', 'hospitality'
        ]

        # Technical skill indicators that would make it a tech job
        tech_skill_indicators = [
            'programming', 'coding', 'software', 'python', 'java', 'javascript', 'react',
            'angular', 'flutter', 'android', 'ios', 'database', 'sql', 'api', 'git',
            'docker', 'kubernetes', 'aws', 'cloud', 'machine learning', 'data science'
        ]

        job_lower = job_text.lower()

        # Check for non-tech indicators
        has_non_tech_indicators = any(indicator in job_lower for indicator in non_tech_indicators)

        # Check for tech skill requirements
        has_tech_requirements = any(tech in job_lower for tech in tech_skill_indicators)

        # Job is non-tech if it has non-tech indicators and no tech requirements
        return has_non_tech_indicators and not has_tech_requirements

    def _is_qa_job(self, job_text):
        """Detect if a job is specifically a QA/Testing role"""
        qa_indicators = ['qa engineer', 'quality assurance', 'test engineer', 'testing', 'qa analyst']
        job_lower = job_text.lower()
        return any(indicator in job_lower for indicator in qa_indicators)

    def _calculate_experience_alignment(self, resume_text, job_text):
        """Calculate experience level alignment"""
        # Extract experience indicators
        resume_exp_indicators = []
        job_exp_indicators = []

        # Experience patterns
        exp_patterns = {
            'entry': ['entry', 'junior', 'graduate', 'intern', '0-2 years', '1-2 years'],
            'mid': ['mid', 'intermediate', '2-5 years', '3-5 years', '2+ years'],
            'senior': ['senior', 'lead', 'principal', '5+ years', '7+ years', 'expert']
        }

        for level, patterns in exp_patterns.items():
            for pattern in patterns:
                if re.search(r'\b' + re.escape(pattern) + r'\b', resume_text):
                    resume_exp_indicators.append(level)
                if re.search(r'\b' + re.escape(pattern) + r'\b', job_text):
                    job_exp_indicators.append(level)

        # If no clear indicators, return neutral
        if not resume_exp_indicators or not job_exp_indicators:
            return 0.6

        # Check for alignment
        if any(level in job_exp_indicators for level in resume_exp_indicators):
            return 0.8
        else:
            return 0.4

    def _apply_requirement_based_scaling(self, similarity, requirement_score, domain_score):
        """Apply scaling with aggressive penalties for domain mismatches"""
        import math

        # Apply very aggressive domain penalties for non-tech jobs
        if domain_score <= 0.1:
            # Almost zero domain relevance - cap at 35%
            return min(0.35, similarity * 0.3)
        elif domain_score <= 0.2:
            # Very low domain relevance - cap at 40%
            return min(0.40, similarity * 0.4)
        elif domain_score <= 0.4:
            # Low domain relevance - strong penalty
            similarity *= 0.5
        elif domain_score <= 0.6:
            # Moderate domain relevance - moderate penalty
            similarity *= 0.7

        # Apply requirement-based adjustments
        if requirement_score < 0.2:
            # Very poor requirement match - strong cap
            similarity = min(0.35, similarity * 0.6)
        elif requirement_score < 0.4:
            # Poor requirement match - moderate cap
            similarity = min(0.50, similarity * 0.8)
        elif requirement_score < 0.6:
            # Fair requirement match - light penalty
            similarity *= 0.9
        elif requirement_score > 0.8:
            # Excellent requirement match - boost score
            similarity *= 1.25
        elif requirement_score > 0.6:
            # Good requirement match - moderate boost
            similarity *= 1.1

        # Apply sigmoid scaling for better distribution
        # Use moderate sigmoid to maintain good spread
        scaled = 1 / (1 + math.exp(-5 * (similarity - 0.5)))

        # Map to realistic range: 15% to 90%
        final_score = 0.15 + (scaled * 0.75)

        return min(0.90, max(0.15, final_score))

    def _apply_improved_scaling(self, similarity):
        """Apply improved scaling for embedding-only similarity"""
        import math

        # Apply more aggressive scaling to spread out scores
        # Use a power function to reduce high similarities and boost low ones
        if similarity > 0.7:
            # Reduce very high similarities
            scaled = 0.7 + (similarity - 0.7) * 0.5
        else:
            # Keep lower similarities more linear
            scaled = similarity

        # Apply sigmoid transformation for better distribution
        sigmoid_scaled = 1 / (1 + math.exp(-8 * (scaled - 0.5)))

        # Map to range 0.2 to 0.85
        final_score = 0.2 + (sigmoid_scaled * 0.65)

        return min(0.85, max(0.20, final_score))
