#!/bin/bash

echo "🚀 Starting ResuMatch Application..."

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to start backend
start_backend() {
    echo "📡 Starting ResuMatch Backend..."
    
    # Check if backend is already running
    if check_port 8000; then
        echo "✅ Backend already running on port 8000"
        return 0
    fi
    
    # Check if Python is available
    if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
        echo "❌ Python not found. Please install Python 3.8+"
        return 1
    fi
    
    # Use python3 if available, otherwise python
    PYTHON_CMD="python3"
    if ! command -v python3 &> /dev/null; then
        PYTHON_CMD="python"
    fi
    
    echo "🐍 Using Python: $($PYTHON_CMD --version)"
    
    # Start backend in background
    echo "🔄 Starting FastAPI backend..."
    nohup $PYTHON_CMD main.py --host 0.0.0.0 --port 8000 > backend.log 2>&1 &
    BACKEND_PID=$!
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/ > /dev/null 2>&1; then
            echo "✅ Backend started successfully on http://localhost:8000"
            echo "📋 Backend PID: $BACKEND_PID"
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo ""
    echo "❌ Backend failed to start. Check backend.log for details."
    return 1
}

# Function to start frontend
start_frontend() {
    echo "🎨 Starting ResuMatch Frontend..."
    
    # Check if frontend is already running
    if check_port 3000; then
        echo "✅ Frontend already running on port 3000"
        return 0
    fi
    
    # Navigate to frontend directory
    if [ ! -d "frontend" ]; then
        echo "❌ Frontend directory not found. Please run this script from the ResuMatch root directory."
        return 1
    fi
    
    cd frontend
    
    # Check if Node.js is available
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js not found. Please install Node.js 18+"
        return 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js version 18+ required. Current: $(node -v)"
        return 1
    fi
    
    echo "📦 Node.js version: $(node -v)"
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing frontend dependencies..."
        npm install
        if [ $? -ne 0 ]; then
            echo "❌ Failed to install dependencies"
            return 1
        fi
    fi
    
    # Check if .env.local exists
    if [ ! -f ".env.local" ]; then
        echo "📝 Creating environment file..."
        cp .env.example .env.local
    fi
    
    # Start frontend in background
    echo "🔄 Starting Next.js frontend..."
    nohup npm run dev > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    echo "⏳ Waiting for frontend to start..."
    for i in {1..60}; do
        if curl -s http://localhost:3000/ > /dev/null 2>&1; then
            echo "✅ Frontend started successfully on http://localhost:3000"
            echo "📋 Frontend PID: $FRONTEND_PID"
            cd ..
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo ""
    echo "❌ Frontend failed to start. Check frontend.log for details."
    cd ..
    return 1
}

# Function to show status
show_status() {
    echo ""
    echo "📊 ResuMatch Application Status:"
    echo "================================"
    
    if check_port 8000; then
        echo "✅ Backend: Running on http://localhost:8000"
        echo "   📚 API Docs: http://localhost:8000/docs"
    else
        echo "❌ Backend: Not running"
    fi
    
    if check_port 3000; then
        echo "✅ Frontend: Running on http://localhost:3000"
    else
        echo "❌ Frontend: Not running"
    fi
    
    echo ""
}

# Function to stop services
stop_services() {
    echo "🛑 Stopping ResuMatch services..."
    
    # Stop processes on ports 8000 and 3000
    if check_port 8000; then
        echo "🔄 Stopping backend..."
        pkill -f "python.*main.py" || true
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    fi
    
    if check_port 3000; then
        echo "🔄 Stopping frontend..."
        pkill -f "npm.*run.*dev" || true
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    fi
    
    echo "✅ Services stopped"
}

# Main script logic
case "${1:-start}" in
    "start")
        echo "🎯 Starting ResuMatch Application..."
        
        # Start backend first
        if start_backend; then
            # Start frontend
            if start_frontend; then
                show_status
                echo "🎉 ResuMatch is now running!"
                echo ""
                echo "🌐 Open your browser and visit:"
                echo "   Frontend: http://localhost:3000"
                echo "   Backend API: http://localhost:8000"
                echo "   API Documentation: http://localhost:8000/docs"
                echo ""
                echo "📝 Logs:"
                echo "   Backend: backend.log"
                echo "   Frontend: frontend.log"
                echo ""
                echo "🛑 To stop: ./start-resumatch.sh stop"
            else
                echo "❌ Failed to start frontend"
                exit 1
            fi
        else
            echo "❌ Failed to start backend"
            exit 1
        fi
        ;;
    "stop")
        stop_services
        ;;
    "status")
        show_status
        ;;
    "restart")
        stop_services
        sleep 2
        $0 start
        ;;
    *)
        echo "Usage: $0 {start|stop|status|restart}"
        echo ""
        echo "Commands:"
        echo "  start   - Start both backend and frontend"
        echo "  stop    - Stop both services"
        echo "  status  - Show current status"
        echo "  restart - Restart both services"
        exit 1
        ;;
esac
